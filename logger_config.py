import logging
import os
from datetime import datetime

def setup_logger(name, log_dir='logs'):
    """设置日志记录器
    
    Args:
        name: 日志记录器名称
        log_dir: 日志文件保存目录
    
    Returns:
        logger: 配置好的日志记录器
    """
    # 获取当前文件所在目录的绝对路径
    current_dir = os.path.dirname(os.path.abspath(__file__))
    # 构建日志目录的绝对路径
    log_dir = os.path.join(current_dir, log_dir)
    
    # 创建日志目录
    if not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir)
            print(f"创建日志目录: {log_dir}")
        except Exception as e:
            print(f"创建日志目录失败: {e}")
            # 如果创建失败，使用当前目录
            log_dir = current_dir
    
    # 生成日志文件名，包含日期
    current_date = datetime.now().strftime('%Y%m%d')
    log_file = os.path.join(log_dir, f'{name}_{current_date}.log')
    
    # 创建日志记录器
    logger = logging.getLogger(name)
    logger.setLevel(logging.INFO)
    
    # 如果已经有处理器，先清除
    if logger.handlers:
        logger.handlers.clear()
    
    # 创建文件处理器
    try:
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.INFO)
        
        # 创建控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        
        # 创建格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 设置处理器的格式化器
        file_handler.setFormatter(formatter)
        console_handler.setFormatter(formatter)
        
        # 添加处理器到日志记录器
        logger.addHandler(file_handler)
        logger.addHandler(console_handler)
        
        print(f"日志文件已创建: {log_file}")
        return logger
    except Exception as e:
        print(f"创建日志处理器失败: {e}")
        # 如果创建文件处理器失败，只使用控制台处理器
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.INFO)
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        console_handler.setFormatter(formatter)
        logger.addHandler(console_handler)
        return logger 