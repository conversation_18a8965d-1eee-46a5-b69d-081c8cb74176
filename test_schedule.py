#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
定时任务测试脚本
用于验证定时任务是否正常工作
"""

import time
from datetime import datetime
import schedule
from main import get_local_time, is_trading_time, schedule_jobs

def test_time_check():
    """测试时间检查功能"""
    print("=== 时间检查测试 ===")
    current_time = get_local_time()
    print(f"当前时间: {current_time}")
    print(f"是否为交易时间: {is_trading_time()}")
    
    # 测试不同时间段
    test_times = [
        "09:05:00",  # 上午开盘
        "12:00:00",  # 午休时间
        "14:30:00",  # 下午交易
        "21:30:00",  # 夜盘
        "03:00:00",  # 凌晨
    ]
    
    for time_str in test_times:
        test_time = datetime.strptime(time_str, "%H:%M:%S").time()
        print(f"测试时间 {time_str}: {'交易时间' if test_time else '非交易时间'}")

def test_schedule():
    """测试定时任务设置"""
    print("\n=== 定时任务测试 ===")
    
    # 清除所有任务
    schedule.clear()
    
    # 设置测试任务
    def test_job():
        current_time = get_local_time()
        print(f"定时任务执行: {current_time}")
        print(f"是否为交易时间: {is_trading_time()}")
    
    # 设置一个1分钟后执行的任务
    schedule.every(1).minutes.do(test_job)
    print("已设置1分钟后执行的测试任务")
    
    # 运行5分钟
    print("开始运行定时任务（5分钟）...")
    start_time = time.time()
    while time.time() - start_time < 300:  # 5分钟
        schedule.run_pending()
        time.sleep(1)

def test_main_schedule():
    """测试主程序的定时任务设置"""
    print("\n=== 主程序定时任务测试 ===")
    
    # 清除所有任务
    schedule.clear()
    
    # 调用主程序的定时任务设置
    schedule_jobs()
    
    # 显示所有已设置的任务
    print("已设置的定时任务:")
    for job in schedule.jobs:
        print(f"- {job}")

if __name__ == "__main__":
    print("定时任务测试开始...")
    print(f"当前系统时间: {get_local_time()}")
    
    # 测试时间检查
    test_time_check()
    
    # 测试主程序定时任务设置
    test_main_schedule()
    
    # 测试定时任务执行
    test_schedule()
    
    print("\n测试完成！") 