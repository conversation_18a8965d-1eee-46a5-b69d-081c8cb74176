2025-06-20 15:42:54 - contract_fetcher - INFO - 主合约: ['CZCE.CY507', 'CZCE.CY509', 'CFFEX.TS2512', 'CFFEX.TS2509', 'CZCE.TA601', 'CZCE.TA509', 'CZCE.CF601', 'CZCE.CF509', 'DCE.y2509', 'DCE.y2601', 'SHFE.au2510', 'SHFE.au2508', 'CFFEX.IC2509', 'CZCE.FG509', 'CZCE.FG601', 'SHFE.cu2508', 'SHFE.cu2507', 'SHFE.al2507', 'SHFE.al2508', 'DCE.cs2507', 'DCE.cs2509', 'SHFE.ru2601', 'SHFE.ru2509', 'DCE.b2509', 'DCE.b2508', 'CZCE.RM509', 'CZCE.RM601', 'SHFE.hc2510', 'SHFE.hc2511', 'INE.bc2507', 'INE.bc2508', 'DCE.v2601', 'DCE.v2509', 'SHFE.rb2510', 'SHFE.rb2509', 'SHFE.ag2510', 'SHFE.ag2508', 'SHFE.pb2508', 'SHFE.pb2507', 'DCE.l2601', 'DCE.l2509', 'SHFE.sp2509', 'SHFE.sp2507', 'CFFEX.IH2509', 'SHFE.fu2507', 'SHFE.fu2509', 'SHFE.zn2507', 'SHFE.zn2508', 'SHFE.ni2507', 'SHFE.ni2508', 'DCE.jm2509', 'DCE.jm2601', 'DCE.eg2509', 'DCE.eg2601', 'INE.nr2507', 'INE.nr2508', 'DCE.a2507', 'DCE.a2509', 'DCE.m2509', 'DCE.m2601', 'SHFE.bu2509', 'SHFE.bu2507', 'GFEX.lc2509', 'GFEX.lc2507', 'SHFE.sn2508', 'SHFE.sn2507', 'CFFEX.IF2509', 'DCE.i2601', 'DCE.i2509', 'CZCE.SA509', 'CZCE.SA601', 'DCE.pg2507', 'DCE.pg2508', 'INE.lu2508', 'INE.lu2509', 'CFFEX.IM2509', 'CZCE.PF507', 'CZCE.PF508', 'CZCE.MA509', 'CZCE.MA601', 'SHFE.ss2507', 'SHFE.ss2508', 'CFFEX.T2509', 'CFFEX.T2512', 'CZCE.SR509', 'CZCE.SR511', 'CFFEX.TF2509', 'CFFEX.TF2512', 'DCE.j2601', 'DCE.j2509', 'DCE.c2509', 'DCE.c2507', 'SHFE.ao2510', 'SHFE.ao2509', 'DCE.p2601', 'DCE.p2509', 'GFEX.si2509', 'GFEX.si2507', 'DCE.eb2507', 'DCE.eb2508', 'INE.sc2508', 'INE.sc2507', 'SHFE.ad2511', 'SHFE.ad2512', 'CZCE.SH509', 'CZCE.SH508', 'CZCE.PR509', 'CZCE.PR508', 'CFFEX.TL2512', 'CFFEX.TL2509', 'CZCE.OI509', 'CZCE.OI601', 'SHFE.br2508', 'SHFE.br2507', 'DCE.pp2509', 'DCE.pp2601', 'CZCE.PX511', 'CZCE.PX509', 'GFEX.ps2507', 'GFEX.ps2508', 'CZCE.UR509', 'CZCE.UR601']
2025-06-20 15:42:54 - contract_fetcher - INFO - 到期时间在45天内的合约: ['SHFE.au2507', 'SHFE.cu2507', 'SHFE.al2507', 'SHFE.ru2507', 'SHFE.rb2507', 'SHFE.ag2507', 'SHFE.pb2507', 'SHFE.zn2507', 'SHFE.ni2507', 'SHFE.sn2507', 'SHFE.ao2507', 'INE.sc2508', 'SHFE.br2507']
2025-06-20 15:42:54 - contract_fetcher - INFO - 主合约,并且期权有效期在80天内的: ['CZCE.CY507', 'CZCE.CY509', 'CFFEX.TS2512', 'CFFEX.TS2509', 'CZCE.TA509', 'CZCE.CF509', 'DCE.y2509', 'SHFE.au2508', 'CFFEX.IC2509', 'CFFEX.IC2506', 'CZCE.FG509', 'SHFE.cu2508', 'SHFE.cu2507', 'SHFE.al2507', 'SHFE.al2508', 'DCE.cs2507', 'DCE.cs2509', 'SHFE.ru2509', 'DCE.b2509', 'DCE.b2508', 'CZCE.RM509', 'SHFE.hc2510', 'SHFE.hc2511', 'INE.bc2507', 'INE.bc2508', 'DCE.v2509', 'SHFE.rb2509', 'SHFE.ag2508', 'SHFE.pb2508', 'SHFE.pb2507', 'DCE.l2509', 'SHFE.sp2509', 'SHFE.sp2507', 'CFFEX.IH2509', 'CFFEX.IH2506', 'SHFE.fu2507', 'SHFE.fu2509', 'SHFE.zn2507', 'SHFE.zn2508', 'SHFE.ni2507', 'SHFE.ni2508', 'DCE.jm2509', 'DCE.jm2601', 'DCE.eg2509', 'INE.nr2507', 'INE.nr2508', 'DCE.a2507', 'DCE.a2509', 'DCE.m2509', 'SHFE.bu2509', 'SHFE.bu2507', 'GFEX.lc2509', 'GFEX.lc2507', 'CZCE.UR506', 'SHFE.sn2508', 'SHFE.sn2507', 'CFFEX.IF2509', 'CFFEX.IF2506', 'DCE.i2509', 'CZCE.SA509', 'DCE.pg2507', 'DCE.pg2508', 'INE.lu2508', 'INE.lu2509', 'CFFEX.IM2506', 'CFFEX.IM2509', 'CZCE.PF507', 'CZCE.PF508', 'CZCE.MA509', 'SHFE.ss2507', 'SHFE.ss2508', 'CFFEX.T2509', 'CFFEX.T2512', 'CZCE.SR509', 'CFFEX.TF2509', 'CFFEX.TF2512', 'DCE.j2601', 'DCE.j2509', 'DCE.c2509', 'DCE.c2507', 'SHFE.ao2509', 'DCE.p2509', 'GFEX.si2509', 'GFEX.si2507', 'DCE.eb2507', 'DCE.eb2508', 'INE.sc2508', 'INE.sc2507', 'CZCE.SH509', 'CZCE.SH508', 'CZCE.PR509', 'CZCE.PR508', 'CFFEX.TL2512', 'CFFEX.TL2509', 'CZCE.OI509', 'SHFE.br2508', 'SHFE.br2507', 'DCE.pp2509', 'CZCE.PX509', 'GFEX.ps2507', 'GFEX.ps2508', 'CZCE.UR509']
2025-06-20 15:42:54 - contract_fetcher - INFO - 合并的一个合约: ['DCE.v2509', 'CFFEX.T2512', 'CZCE.RM601', 'SHFE.ao2507', 'DCE.pp2601', 'CFFEX.T2509', 'DCE.a2507', 'CZCE.CY509', 'SHFE.rb2509', 'DCE.c2507', 'SHFE.au2510', 'CFFEX.TF2509', 'DCE.j2509', 'CZCE.CF601', 'DCE.cs2507', 'INE.nr2508', 'CZCE.SH508', 'DCE.l2601', 'DCE.y2509', 'INE.sc2507', 'CZCE.MA509', 'SHFE.ag2510', 'CZCE.PX509', 'INE.sc2508', 'SHFE.sp2507', 'SHFE.ss2508', 'CZCE.PF508', 'CFFEX.TF2512', 'CZCE.CY507', 'GFEX.si2507', 'SHFE.fu2507', 'DCE.i2509', 'SHFE.ru2601', 'INE.bc2508', 'DCE.b2509', 'CZCE.SH509', 'CFFEX.IH2506', 'CZCE.FG509', 'CZCE.SA509', 'SHFE.sp2509', 'SHFE.zn2507', 'CZCE.UR509', 'CFFEX.IC2509', 'SHFE.fu2509', 'SHFE.au2508', 'SHFE.al2508', 'DCE.a2509', 'INE.nr2507', 'SHFE.rb2510', 'DCE.pg2507', 'SHFE.cu2507', 'SHFE.hc2510', 'SHFE.al2507', 'DCE.cs2509', 'CZCE.PX511', 'SHFE.ad2512', 'SHFE.ag2508', 'SHFE.bu2507', 'SHFE.pb2508', 'CZCE.TA509', 'SHFE.bu2509', 'DCE.p2509', 'DCE.p2601', 'SHFE.ru2507', 'DCE.eb2507', 'CZCE.SA601', 'CFFEX.TS2512', 'DCE.j2601', 'DCE.m2509', 'CFFEX.TL2509', 'CZCE.OI601', 'CZCE.PR508', 'GFEX.lc2507', 'SHFE.ni2508', 'DCE.m2601', 'CFFEX.IH2509', 'CFFEX.TS2509', 'DCE.eg2601', 'SHFE.ni2507', 'SHFE.ss2507', 'CZCE.PR509', 'CZCE.UR506', 'CZCE.MA601', 'DCE.eb2508', 'SHFE.pb2507', 'DCE.v2601', 'SHFE.au2507', 'CZCE.PF507', 'CZCE.OI509', 'CFFEX.IF2506', 'CZCE.TA601', 'INE.lu2509', 'GFEX.lc2509', 'SHFE.cu2508', 'DCE.eg2509', 'DCE.y2601', 'DCE.jm2601', 'DCE.jm2509', 'DCE.i2601', 'SHFE.br2508', 'CZCE.SR511', 'SHFE.ao2510', 'CFFEX.TL2512', 'DCE.c2509', 'SHFE.ru2509', 'SHFE.rb2507', 'SHFE.zn2508', 'DCE.pp2509', 'CFFEX.IF2509', 'SHFE.ad2511', 'CFFEX.IM2509', 'CFFEX.IM2506', 'GFEX.si2509', 'DCE.l2509', 'CZCE.SR509', 'SHFE.hc2511', 'GFEX.ps2508', 'INE.bc2507', 'DCE.pg2508', 'SHFE.sn2508', 'CZCE.UR601', 'SHFE.ao2509', 'CZCE.RM509', 'SHFE.br2507', 'INE.lu2508', 'SHFE.sn2507', 'GFEX.ps2507', 'SHFE.ag2507', 'CFFEX.IC2506', 'DCE.b2508', 'CZCE.FG601', 'CZCE.CF509']
2025-06-20 15:42:54 - contract_fetcher - INFO - 合并的一个合约,并且期权有效期在80天内的: ['DCE.v2509', 'CFFEX.T2512', 'SHFE.ao2507', 'CFFEX.T2509', 'DCE.a2507', 'CZCE.CY509', 'SHFE.rb2509', 'DCE.c2507', 'CFFEX.TF2509', 'DCE.j2509', 'INE.nr2508', 'DCE.cs2507', 'CZCE.SH508', 'DCE.y2509', 'INE.sc2507', 'CZCE.MA509', 'CZCE.PX509', 'INE.sc2508', 'SHFE.sp2507', 'SHFE.ss2508', 'CZCE.PF508', 'CFFEX.TF2512', 'CZCE.CY507', 'GFEX.si2507', 'SHFE.fu2507', 'DCE.i2509', 'INE.bc2508', 'DCE.b2509', 'CZCE.SH509', 'CFFEX.IH2506', 'CZCE.FG509', 'CZCE.SA509', 'SHFE.sp2509', 'SHFE.zn2507', 'CZCE.UR509', 'CFFEX.IC2509', 'SHFE.fu2509', 'SHFE.au2508', 'SHFE.al2508', 'DCE.a2509', 'INE.nr2507', 'DCE.pg2507', 'SHFE.cu2507', 'SHFE.hc2510', 'SHFE.al2507', 'DCE.cs2509', 'SHFE.ag2508', 'SHFE.bu2507', 'SHFE.pb2508', 'CZCE.TA509', 'SHFE.bu2509', 'DCE.p2509', 'SHFE.ru2507', 'DCE.eb2507', 'CFFEX.TS2512', 'DCE.j2601', 'DCE.m2509', 'CFFEX.TL2509', 'CZCE.PR508', 'GFEX.lc2507', 'SHFE.ni2508', 'CFFEX.IH2509', 'CFFEX.TS2509', 'SHFE.ni2507', 'SHFE.ss2507', 'CZCE.PR509', 'CZCE.UR506', 'DCE.eb2508', 'SHFE.pb2507', 'SHFE.au2507', 'CZCE.PF507', 'CZCE.OI509', 'CFFEX.IF2506', 'INE.lu2509', 'GFEX.lc2509', 'SHFE.cu2508', 'DCE.eg2509', 'DCE.jm2601', 'DCE.jm2509', 'SHFE.br2508', 'CFFEX.TL2512', 'DCE.c2509', 'SHFE.ru2509', 'SHFE.rb2507', 'SHFE.zn2508', 'DCE.pp2509', 'CFFEX.IF2509', 'CFFEX.IM2509', 'CFFEX.IM2506', 'GFEX.si2509', 'DCE.l2509', 'CZCE.SR509', 'SHFE.hc2511', 'GFEX.ps2508', 'INE.bc2507', 'DCE.pg2508', 'SHFE.sn2508', 'SHFE.ao2509', 'CZCE.RM509', 'SHFE.br2507', 'INE.lu2508', 'SHFE.sn2507', 'GFEX.ps2507', 'CFFEX.IC2506', 'SHFE.ag2507', 'DCE.b2508', 'CZCE.CF509']
2025-06-20 15:42:54 - contract_fetcher - INFO - 当前小时时间: 15
2025-06-20 15:42:54 - contract_fetcher - INFO - 过滤前品种列表: 132
2025-06-20 15:42:54 - contract_fetcher - INFO - 过滤后品种列表: 132
2025-06-20 15:42:54 - contract_fetcher - INFO - 当前小时时间: 15
2025-06-20 15:42:54 - contract_fetcher - INFO - 过滤前品种列表: 107
2025-06-20 15:42:54 - contract_fetcher - INFO - 过滤后品种列表: 107
2025-06-20 15:42:54 - contract_fetcher - INFO - 获取到 975 个未过期的期权合约----用于采集数据
2025-06-20 15:42:54 - contract_fetcher - INFO - 当前小时时间: 15
2025-06-20 15:42:54 - contract_fetcher - INFO - 过滤前品种列表: 975
2025-06-20 15:42:54 - contract_fetcher - INFO - 过滤后品种列表: 975
2025-06-20 15:42:54 - contract_fetcher - INFO - 时间过滤后剩余 975 个期权合约----用于采集数据
2025-06-20 15:42:54 - contract_fetcher - INFO - 主合约: ['CZCE.CY507', 'CZCE.CY509', 'CFFEX.TS2512', 'CFFEX.TS2509', 'CZCE.TA601', 'CZCE.TA509', 'CZCE.CF601', 'CZCE.CF509', 'DCE.y2509', 'DCE.y2601', 'SHFE.au2510', 'SHFE.au2508', 'CFFEX.IC2509', 'CZCE.FG509', 'CZCE.FG601', 'SHFE.cu2508', 'SHFE.cu2507', 'SHFE.al2507', 'SHFE.al2508', 'DCE.cs2507', 'DCE.cs2509', 'SHFE.ru2601', 'SHFE.ru2509', 'DCE.b2509', 'DCE.b2508', 'CZCE.RM509', 'CZCE.RM601', 'SHFE.hc2510', 'SHFE.hc2511', 'INE.bc2507', 'INE.bc2508', 'DCE.v2601', 'DCE.v2509', 'SHFE.rb2510', 'SHFE.rb2509', 'SHFE.ag2510', 'SHFE.ag2508', 'SHFE.pb2508', 'SHFE.pb2507', 'DCE.l2601', 'DCE.l2509', 'SHFE.sp2509', 'SHFE.sp2507', 'CFFEX.IH2509', 'SHFE.fu2507', 'SHFE.fu2509', 'SHFE.zn2507', 'SHFE.zn2508', 'SHFE.ni2507', 'SHFE.ni2508', 'DCE.jm2509', 'DCE.jm2601', 'DCE.eg2509', 'DCE.eg2601', 'INE.nr2507', 'INE.nr2508', 'DCE.a2507', 'DCE.a2509', 'DCE.m2509', 'DCE.m2601', 'SHFE.bu2509', 'SHFE.bu2507', 'GFEX.lc2509', 'GFEX.lc2507', 'SHFE.sn2508', 'SHFE.sn2507', 'CFFEX.IF2509', 'DCE.i2601', 'DCE.i2509', 'CZCE.SA509', 'CZCE.SA601', 'DCE.pg2507', 'DCE.pg2508', 'INE.lu2508', 'INE.lu2509', 'CFFEX.IM2509', 'CZCE.PF507', 'CZCE.PF508', 'CZCE.MA509', 'CZCE.MA601', 'SHFE.ss2507', 'SHFE.ss2508', 'CFFEX.T2509', 'CFFEX.T2512', 'CZCE.SR509', 'CZCE.SR511', 'CFFEX.TF2509', 'CFFEX.TF2512', 'DCE.j2601', 'DCE.j2509', 'DCE.c2509', 'DCE.c2507', 'SHFE.ao2510', 'SHFE.ao2509', 'DCE.p2601', 'DCE.p2509', 'GFEX.si2509', 'GFEX.si2507', 'DCE.eb2507', 'DCE.eb2508', 'INE.sc2508', 'INE.sc2507', 'SHFE.ad2511', 'SHFE.ad2512', 'CZCE.SH509', 'CZCE.SH508', 'CZCE.PR509', 'CZCE.PR508', 'CFFEX.TL2512', 'CFFEX.TL2509', 'CZCE.OI509', 'CZCE.OI601', 'SHFE.br2508', 'SHFE.br2507', 'DCE.pp2509', 'DCE.pp2601', 'CZCE.PX511', 'CZCE.PX509', 'GFEX.ps2507', 'GFEX.ps2508', 'CZCE.UR509', 'CZCE.UR601']
2025-06-20 15:42:54 - contract_fetcher - INFO - 到期时间在45天内的合约: ['SHFE.au2507', 'SHFE.cu2507', 'SHFE.al2507', 'SHFE.ru2507', 'SHFE.rb2507', 'SHFE.ag2507', 'SHFE.pb2507', 'SHFE.zn2507', 'SHFE.ni2507', 'SHFE.sn2507', 'SHFE.ao2507', 'INE.sc2508', 'SHFE.br2507']
2025-06-20 15:42:54 - contract_fetcher - INFO - 主合约,并且期权有效期在80天内的: ['CZCE.CY507', 'CZCE.CY509', 'CFFEX.TS2512', 'CFFEX.TS2509', 'CZCE.TA509', 'CZCE.CF509', 'DCE.y2509', 'SHFE.au2508', 'CFFEX.IC2509', 'CFFEX.IC2506', 'CZCE.FG509', 'SHFE.cu2508', 'SHFE.cu2507', 'SHFE.al2507', 'SHFE.al2508', 'DCE.cs2507', 'DCE.cs2509', 'SHFE.ru2509', 'DCE.b2509', 'DCE.b2508', 'CZCE.RM509', 'SHFE.hc2510', 'SHFE.hc2511', 'INE.bc2507', 'INE.bc2508', 'DCE.v2509', 'SHFE.rb2509', 'SHFE.ag2508', 'SHFE.pb2508', 'SHFE.pb2507', 'DCE.l2509', 'SHFE.sp2509', 'SHFE.sp2507', 'CFFEX.IH2509', 'CFFEX.IH2506', 'SHFE.fu2507', 'SHFE.fu2509', 'SHFE.zn2507', 'SHFE.zn2508', 'SHFE.ni2507', 'SHFE.ni2508', 'DCE.jm2509', 'DCE.jm2601', 'DCE.eg2509', 'INE.nr2507', 'INE.nr2508', 'DCE.a2507', 'DCE.a2509', 'DCE.m2509', 'SHFE.bu2509', 'SHFE.bu2507', 'GFEX.lc2509', 'GFEX.lc2507', 'CZCE.UR506', 'SHFE.sn2508', 'SHFE.sn2507', 'CFFEX.IF2509', 'CFFEX.IF2506', 'DCE.i2509', 'CZCE.SA509', 'DCE.pg2507', 'DCE.pg2508', 'INE.lu2508', 'INE.lu2509', 'CFFEX.IM2506', 'CFFEX.IM2509', 'CZCE.PF507', 'CZCE.PF508', 'CZCE.MA509', 'SHFE.ss2507', 'SHFE.ss2508', 'CFFEX.T2509', 'CFFEX.T2512', 'CZCE.SR509', 'CFFEX.TF2509', 'CFFEX.TF2512', 'DCE.j2601', 'DCE.j2509', 'DCE.c2509', 'DCE.c2507', 'SHFE.ao2509', 'DCE.p2509', 'GFEX.si2509', 'GFEX.si2507', 'DCE.eb2507', 'DCE.eb2508', 'INE.sc2508', 'INE.sc2507', 'CZCE.SH509', 'CZCE.SH508', 'CZCE.PR509', 'CZCE.PR508', 'CFFEX.TL2512', 'CFFEX.TL2509', 'CZCE.OI509', 'SHFE.br2508', 'SHFE.br2507', 'DCE.pp2509', 'CZCE.PX509', 'GFEX.ps2507', 'GFEX.ps2508', 'CZCE.UR509']
2025-06-20 15:42:54 - contract_fetcher - INFO - 合并的一个合约: ['DCE.v2509', 'CFFEX.T2512', 'CZCE.RM601', 'SHFE.ao2507', 'DCE.pp2601', 'CFFEX.T2509', 'DCE.a2507', 'CZCE.CY509', 'SHFE.rb2509', 'DCE.c2507', 'SHFE.au2510', 'CFFEX.TF2509', 'DCE.j2509', 'CZCE.CF601', 'DCE.cs2507', 'INE.nr2508', 'CZCE.SH508', 'DCE.l2601', 'DCE.y2509', 'INE.sc2507', 'CZCE.MA509', 'SHFE.ag2510', 'CZCE.PX509', 'INE.sc2508', 'SHFE.sp2507', 'SHFE.ss2508', 'CZCE.PF508', 'CFFEX.TF2512', 'CZCE.CY507', 'GFEX.si2507', 'SHFE.fu2507', 'DCE.i2509', 'SHFE.ru2601', 'INE.bc2508', 'DCE.b2509', 'CZCE.SH509', 'CFFEX.IH2506', 'CZCE.FG509', 'CZCE.SA509', 'SHFE.sp2509', 'SHFE.zn2507', 'CZCE.UR509', 'CFFEX.IC2509', 'SHFE.fu2509', 'SHFE.au2508', 'SHFE.al2508', 'DCE.a2509', 'INE.nr2507', 'SHFE.rb2510', 'DCE.pg2507', 'SHFE.cu2507', 'SHFE.hc2510', 'SHFE.al2507', 'DCE.cs2509', 'CZCE.PX511', 'SHFE.ad2512', 'SHFE.ag2508', 'SHFE.bu2507', 'SHFE.pb2508', 'CZCE.TA509', 'SHFE.bu2509', 'DCE.p2509', 'DCE.p2601', 'SHFE.ru2507', 'DCE.eb2507', 'CZCE.SA601', 'CFFEX.TS2512', 'DCE.j2601', 'DCE.m2509', 'CFFEX.TL2509', 'CZCE.OI601', 'CZCE.PR508', 'GFEX.lc2507', 'SHFE.ni2508', 'DCE.m2601', 'CFFEX.IH2509', 'CFFEX.TS2509', 'DCE.eg2601', 'SHFE.ni2507', 'SHFE.ss2507', 'CZCE.PR509', 'CZCE.UR506', 'CZCE.MA601', 'DCE.eb2508', 'SHFE.pb2507', 'DCE.v2601', 'SHFE.au2507', 'CZCE.PF507', 'CZCE.OI509', 'CFFEX.IF2506', 'CZCE.TA601', 'INE.lu2509', 'GFEX.lc2509', 'SHFE.cu2508', 'DCE.eg2509', 'DCE.y2601', 'DCE.jm2601', 'DCE.jm2509', 'DCE.i2601', 'SHFE.br2508', 'CZCE.SR511', 'SHFE.ao2510', 'CFFEX.TL2512', 'DCE.c2509', 'SHFE.ru2509', 'SHFE.rb2507', 'SHFE.zn2508', 'DCE.pp2509', 'CFFEX.IF2509', 'SHFE.ad2511', 'CFFEX.IM2509', 'CFFEX.IM2506', 'GFEX.si2509', 'DCE.l2509', 'CZCE.SR509', 'SHFE.hc2511', 'GFEX.ps2508', 'INE.bc2507', 'DCE.pg2508', 'SHFE.sn2508', 'CZCE.UR601', 'SHFE.ao2509', 'CZCE.RM509', 'SHFE.br2507', 'INE.lu2508', 'SHFE.sn2507', 'GFEX.ps2507', 'SHFE.ag2507', 'CFFEX.IC2506', 'DCE.b2508', 'CZCE.FG601', 'CZCE.CF509']
2025-06-20 15:42:54 - contract_fetcher - INFO - 合并的一个合约,并且期权有效期在80天内的: ['DCE.v2509', 'CFFEX.T2512', 'SHFE.ao2507', 'CFFEX.T2509', 'DCE.a2507', 'CZCE.CY509', 'SHFE.rb2509', 'DCE.c2507', 'CFFEX.TF2509', 'DCE.j2509', 'INE.nr2508', 'DCE.cs2507', 'CZCE.SH508', 'DCE.y2509', 'INE.sc2507', 'CZCE.MA509', 'CZCE.PX509', 'INE.sc2508', 'SHFE.sp2507', 'SHFE.ss2508', 'CZCE.PF508', 'CFFEX.TF2512', 'CZCE.CY507', 'GFEX.si2507', 'SHFE.fu2507', 'DCE.i2509', 'INE.bc2508', 'DCE.b2509', 'CZCE.SH509', 'CFFEX.IH2506', 'CZCE.FG509', 'CZCE.SA509', 'SHFE.sp2509', 'SHFE.zn2507', 'CZCE.UR509', 'CFFEX.IC2509', 'SHFE.fu2509', 'SHFE.au2508', 'SHFE.al2508', 'DCE.a2509', 'INE.nr2507', 'DCE.pg2507', 'SHFE.cu2507', 'SHFE.hc2510', 'SHFE.al2507', 'DCE.cs2509', 'SHFE.ag2508', 'SHFE.bu2507', 'SHFE.pb2508', 'CZCE.TA509', 'SHFE.bu2509', 'DCE.p2509', 'SHFE.ru2507', 'DCE.eb2507', 'CFFEX.TS2512', 'DCE.j2601', 'DCE.m2509', 'CFFEX.TL2509', 'CZCE.PR508', 'GFEX.lc2507', 'SHFE.ni2508', 'CFFEX.IH2509', 'CFFEX.TS2509', 'SHFE.ni2507', 'SHFE.ss2507', 'CZCE.PR509', 'CZCE.UR506', 'DCE.eb2508', 'SHFE.pb2507', 'SHFE.au2507', 'CZCE.PF507', 'CZCE.OI509', 'CFFEX.IF2506', 'INE.lu2509', 'GFEX.lc2509', 'SHFE.cu2508', 'DCE.eg2509', 'DCE.jm2601', 'DCE.jm2509', 'SHFE.br2508', 'CFFEX.TL2512', 'DCE.c2509', 'SHFE.ru2509', 'SHFE.rb2507', 'SHFE.zn2508', 'DCE.pp2509', 'CFFEX.IF2509', 'CFFEX.IM2509', 'CFFEX.IM2506', 'GFEX.si2509', 'DCE.l2509', 'CZCE.SR509', 'SHFE.hc2511', 'GFEX.ps2508', 'INE.bc2507', 'DCE.pg2508', 'SHFE.sn2508', 'SHFE.ao2509', 'CZCE.RM509', 'SHFE.br2507', 'INE.lu2508', 'SHFE.sn2507', 'GFEX.ps2507', 'CFFEX.IC2506', 'SHFE.ag2507', 'DCE.b2508', 'CZCE.CF509']
2025-06-20 15:42:54 - contract_fetcher - INFO - 当前小时时间: 15
2025-06-20 15:42:54 - contract_fetcher - INFO - 过滤前品种列表: 132
2025-06-20 15:42:54 - contract_fetcher - INFO - 过滤后品种列表: 132
2025-06-20 15:42:54 - contract_fetcher - INFO - 当前小时时间: 15
2025-06-20 15:42:54 - contract_fetcher - INFO - 过滤前品种列表: 107
2025-06-20 15:42:54 - contract_fetcher - INFO - 过滤后品种列表: 107
2025-06-20 15:42:54 - contract_fetcher - INFO - 获取到 975 个未过期的期权合约----用于采集数据
2025-06-20 15:42:54 - contract_fetcher - INFO - 当前小时时间: 15
2025-06-20 15:42:54 - contract_fetcher - INFO - 过滤前品种列表: 975
2025-06-20 15:42:54 - contract_fetcher - INFO - 过滤后品种列表: 975
2025-06-20 15:42:54 - contract_fetcher - INFO - 时间过滤后剩余 975 个期权合约----用于采集数据
2025-06-20 16:02:18 - contract_fetcher - INFO - 主合约: ['CZCE.CY507', 'CZCE.CY509', 'CFFEX.TS2512', 'CFFEX.TS2509', 'CZCE.TA601', 'CZCE.TA509', 'CZCE.CF601', 'CZCE.CF509', 'DCE.y2509', 'DCE.y2601', 'SHFE.au2510', 'SHFE.au2508', 'CFFEX.IC2509', 'CZCE.FG509', 'CZCE.FG601', 'SHFE.cu2508', 'SHFE.cu2507', 'SHFE.al2507', 'SHFE.al2508', 'DCE.cs2507', 'DCE.cs2509', 'SHFE.ru2601', 'SHFE.ru2509', 'DCE.b2509', 'DCE.b2508', 'CZCE.RM509', 'CZCE.RM601', 'SHFE.hc2510', 'SHFE.hc2511', 'INE.bc2507', 'INE.bc2508', 'DCE.v2601', 'DCE.v2509', 'SHFE.rb2510', 'SHFE.rb2509', 'SHFE.ag2510', 'SHFE.ag2508', 'SHFE.pb2508', 'SHFE.pb2507', 'DCE.l2601', 'DCE.l2509', 'SHFE.sp2509', 'SHFE.sp2507', 'CFFEX.IH2509', 'SHFE.fu2507', 'SHFE.fu2509', 'SHFE.zn2507', 'SHFE.zn2508', 'SHFE.ni2507', 'SHFE.ni2508', 'DCE.jm2509', 'DCE.jm2601', 'DCE.eg2509', 'DCE.eg2601', 'INE.nr2507', 'INE.nr2508', 'DCE.a2507', 'DCE.a2509', 'DCE.m2509', 'DCE.m2601', 'SHFE.bu2509', 'SHFE.bu2507', 'GFEX.lc2509', 'GFEX.lc2507', 'SHFE.sn2508', 'SHFE.sn2507', 'CFFEX.IF2509', 'DCE.i2601', 'DCE.i2509', 'CZCE.SA509', 'CZCE.SA601', 'DCE.pg2507', 'DCE.pg2508', 'INE.lu2508', 'INE.lu2509', 'CFFEX.IM2509', 'CZCE.PF507', 'CZCE.PF508', 'CZCE.MA509', 'CZCE.MA601', 'SHFE.ss2507', 'SHFE.ss2508', 'CFFEX.T2509', 'CFFEX.T2512', 'CZCE.SR509', 'CZCE.SR511', 'CFFEX.TF2509', 'CFFEX.TF2512', 'DCE.j2601', 'DCE.j2509', 'DCE.c2509', 'DCE.c2507', 'SHFE.ao2510', 'SHFE.ao2509', 'DCE.p2601', 'DCE.p2509', 'GFEX.si2509', 'GFEX.si2507', 'DCE.eb2507', 'DCE.eb2508', 'INE.sc2508', 'INE.sc2507', 'SHFE.ad2511', 'SHFE.ad2512', 'CZCE.SH509', 'CZCE.SH508', 'CZCE.PR509', 'CZCE.PR508', 'CFFEX.TL2512', 'CFFEX.TL2509', 'CZCE.OI509', 'CZCE.OI601', 'SHFE.br2508', 'SHFE.br2507', 'DCE.pp2509', 'DCE.pp2601', 'CZCE.PX511', 'CZCE.PX509', 'GFEX.ps2507', 'GFEX.ps2508', 'CZCE.UR509', 'CZCE.UR601']
2025-06-20 16:02:18 - contract_fetcher - INFO - 到期时间在45天内的合约: ['SHFE.au2507', 'SHFE.cu2507', 'SHFE.al2507', 'SHFE.ru2507', 'SHFE.rb2507', 'SHFE.ag2507', 'SHFE.pb2507', 'SHFE.zn2507', 'SHFE.ni2507', 'SHFE.sn2507', 'SHFE.ao2507', 'INE.sc2508', 'SHFE.br2507']
2025-06-20 16:02:18 - contract_fetcher - INFO - 主合约,并且期权有效期在80天内的: ['CZCE.CY507', 'CZCE.CY509', 'CFFEX.TS2512', 'CFFEX.TS2509', 'CZCE.TA509', 'CZCE.CF509', 'DCE.y2509', 'SHFE.au2508', 'CFFEX.IC2509', 'CFFEX.IC2506', 'CZCE.FG509', 'SHFE.cu2508', 'SHFE.cu2507', 'SHFE.al2507', 'SHFE.al2508', 'DCE.cs2507', 'DCE.cs2509', 'SHFE.ru2509', 'DCE.b2509', 'DCE.b2508', 'CZCE.RM509', 'SHFE.hc2510', 'SHFE.hc2511', 'INE.bc2507', 'INE.bc2508', 'DCE.v2509', 'SHFE.rb2509', 'SHFE.ag2508', 'SHFE.pb2508', 'SHFE.pb2507', 'DCE.l2509', 'SHFE.sp2509', 'SHFE.sp2507', 'CFFEX.IH2509', 'CFFEX.IH2506', 'SHFE.fu2507', 'SHFE.fu2509', 'SHFE.zn2507', 'SHFE.zn2508', 'SHFE.ni2507', 'SHFE.ni2508', 'DCE.jm2509', 'DCE.jm2601', 'DCE.eg2509', 'INE.nr2507', 'INE.nr2508', 'DCE.a2507', 'DCE.a2509', 'DCE.m2509', 'SHFE.bu2509', 'SHFE.bu2507', 'GFEX.lc2509', 'GFEX.lc2507', 'CZCE.UR506', 'SHFE.sn2508', 'SHFE.sn2507', 'CFFEX.IF2509', 'CFFEX.IF2506', 'DCE.i2509', 'CZCE.SA509', 'DCE.pg2507', 'DCE.pg2508', 'INE.lu2508', 'INE.lu2509', 'CFFEX.IM2506', 'CFFEX.IM2509', 'CZCE.PF507', 'CZCE.PF508', 'CZCE.MA509', 'SHFE.ss2507', 'SHFE.ss2508', 'CFFEX.T2509', 'CFFEX.T2512', 'CZCE.SR509', 'CFFEX.TF2509', 'CFFEX.TF2512', 'DCE.j2601', 'DCE.j2509', 'DCE.c2509', 'DCE.c2507', 'SHFE.ao2509', 'DCE.p2509', 'GFEX.si2509', 'GFEX.si2507', 'DCE.eb2507', 'DCE.eb2508', 'INE.sc2508', 'INE.sc2507', 'CZCE.SH509', 'CZCE.SH508', 'CZCE.PR509', 'CZCE.PR508', 'CFFEX.TL2512', 'CFFEX.TL2509', 'CZCE.OI509', 'SHFE.br2508', 'SHFE.br2507', 'DCE.pp2509', 'CZCE.PX509', 'GFEX.ps2507', 'GFEX.ps2508', 'CZCE.UR509']
2025-06-20 16:02:18 - contract_fetcher - INFO - 合并的一个合约: ['CZCE.CF509', 'CFFEX.TS2512', 'DCE.i2601', 'SHFE.fu2509', 'SHFE.sp2507', 'INE.sc2508', 'CZCE.CY507', 'CFFEX.TL2512', 'SHFE.rb2510', 'SHFE.ru2509', 'INE.bc2507', 'DCE.v2509', 'CZCE.RM509', 'CZCE.TA601', 'SHFE.cu2507', 'GFEX.lc2509', 'CZCE.SR511', 'SHFE.al2508', 'GFEX.si2509', 'GFEX.lc2507', 'SHFE.hc2511', 'SHFE.sn2507', 'SHFE.sn2508', 'CZCE.SA601', 'DCE.l2509', 'CZCE.TA509', 'INE.lu2509', 'SHFE.pb2508', 'CZCE.PR509', 'DCE.a2507', 'DCE.j2601', 'SHFE.ao2507', 'DCE.pg2507', 'INE.lu2508', 'DCE.m2509', 'CZCE.MA601', 'GFEX.ps2507', 'DCE.m2601', 'CFFEX.TF2512', 'DCE.eb2508', 'CZCE.UR601', 'DCE.b2508', 'CFFEX.IF2509', 'CZCE.OI601', 'CFFEX.IC2506', 'GFEX.ps2508', 'SHFE.ad2512', 'CZCE.CF601', 'SHFE.ss2508', 'DCE.y2601', 'CFFEX.IH2506', 'SHFE.rb2507', 'CZCE.PX511', 'DCE.i2509', 'SHFE.rb2509', 'INE.nr2508', 'DCE.pp2509', 'SHFE.br2508', 'CZCE.PR508', 'SHFE.ru2601', 'CFFEX.IM2509', 'DCE.a2509', 'SHFE.ao2509', 'DCE.eg2509', 'SHFE.ag2510', 'SHFE.ru2507', 'SHFE.zn2508', 'CZCE.SA509', 'INE.sc2507', 'DCE.c2507', 'SHFE.ni2508', 'DCE.jm2509', 'GFEX.si2507', 'DCE.b2509', 'DCE.eb2507', 'DCE.p2509', 'SHFE.br2507', 'SHFE.au2507', 'DCE.c2509', 'DCE.l2601', 'CFFEX.TL2509', 'SHFE.hc2510', 'SHFE.cu2508', 'DCE.y2509', 'SHFE.bu2509', 'CZCE.UR506', 'CFFEX.IF2506', 'DCE.pp2601', 'CZCE.PF507', 'CZCE.PF508', 'SHFE.ao2510', 'CZCE.UR509', 'DCE.j2509', 'SHFE.al2507', 'CFFEX.T2509', 'CFFEX.TF2509', 'CZCE.FG509', 'SHFE.ag2508', 'CZCE.OI509', 'CZCE.MA509', 'SHFE.au2508', 'CZCE.RM601', 'SHFE.ad2511', 'CZCE.SR509', 'CZCE.FG601', 'SHFE.ni2507', 'INE.nr2507', 'DCE.pg2508', 'DCE.eg2601', 'SHFE.ss2507', 'DCE.cs2509', 'SHFE.sp2509', 'CFFEX.T2512', 'CFFEX.IH2509', 'CZCE.SH508', 'CFFEX.TS2509', 'INE.bc2508', 'SHFE.ag2507', 'DCE.v2601', 'DCE.cs2507', 'SHFE.zn2507', 'CZCE.PX509', 'SHFE.fu2507', 'CZCE.SH509', 'CFFEX.IC2509', 'SHFE.au2510', 'DCE.jm2601', 'SHFE.pb2507', 'CFFEX.IM2506', 'CZCE.CY509', 'DCE.p2601', 'SHFE.bu2507']
2025-06-20 16:02:18 - contract_fetcher - INFO - 合并的一个合约,并且期权有效期在80天内的: ['CZCE.CF509', 'CFFEX.TS2512', 'SHFE.fu2509', 'SHFE.sp2507', 'INE.sc2508', 'CZCE.CY507', 'CFFEX.TL2512', 'SHFE.ru2509', 'INE.bc2507', 'DCE.v2509', 'CZCE.RM509', 'SHFE.cu2507', 'GFEX.lc2509', 'SHFE.al2508', 'GFEX.si2509', 'GFEX.lc2507', 'SHFE.hc2511', 'SHFE.sn2507', 'SHFE.sn2508', 'DCE.l2509', 'CZCE.TA509', 'INE.lu2509', 'SHFE.pb2508', 'CZCE.PR509', 'DCE.a2507', 'SHFE.ao2507', 'DCE.pg2507', 'INE.lu2508', 'DCE.m2509', 'GFEX.ps2507', 'CFFEX.TF2512', 'DCE.eb2508', 'DCE.b2508', 'CFFEX.IF2509', 'CFFEX.IC2506', 'GFEX.ps2508', 'SHFE.ss2508', 'CFFEX.IH2506', 'SHFE.rb2507', 'DCE.i2509', 'SHFE.rb2509', 'INE.nr2508', 'DCE.pp2509', 'SHFE.br2508', 'CZCE.PR508', 'CFFEX.IM2509', 'DCE.a2509', 'SHFE.ao2509', 'DCE.eg2509', 'SHFE.ru2507', 'SHFE.zn2508', 'CZCE.SA509', 'INE.sc2507', 'DCE.c2507', 'SHFE.ni2508', 'DCE.jm2509', 'GFEX.si2507', 'DCE.b2509', 'DCE.eb2507', 'DCE.p2509', 'SHFE.br2507', 'SHFE.au2507', 'DCE.c2509', 'CFFEX.TL2509', 'SHFE.hc2510', 'SHFE.cu2508', 'DCE.y2509', 'SHFE.bu2509', 'CZCE.UR506', 'CFFEX.IF2506', 'CZCE.PF507', 'CZCE.PF508', 'CZCE.UR509', 'DCE.j2509', 'SHFE.al2507', 'CFFEX.T2509', 'CFFEX.TF2509', 'CZCE.FG509', 'SHFE.ag2508', 'CZCE.OI509', 'CZCE.MA509', 'SHFE.au2508', 'CZCE.SR509', 'SHFE.ni2507', 'INE.nr2507', 'DCE.pg2508', 'SHFE.ss2507', 'DCE.cs2509', 'SHFE.sp2509', 'CFFEX.T2512', 'CFFEX.IH2509', 'CZCE.SH508', 'CFFEX.TS2509', 'INE.bc2508', 'SHFE.ag2507', 'CZCE.CY509', 'DCE.cs2507', 'SHFE.zn2507', 'CZCE.PX509', 'SHFE.fu2507', 'CZCE.SH509', 'CFFEX.IC2509', 'DCE.jm2601', 'SHFE.pb2507', 'CFFEX.IM2506', 'DCE.j2601', 'SHFE.bu2507']
2025-06-20 16:02:18 - contract_fetcher - INFO - 当前小时时间: 16
2025-06-20 16:02:18 - contract_fetcher - INFO - 过滤前品种列表: 132
2025-06-20 16:02:18 - contract_fetcher - INFO - 过滤后品种列表: 132
2025-06-20 16:02:18 - contract_fetcher - INFO - 当前小时时间: 16
2025-06-20 16:02:18 - contract_fetcher - INFO - 过滤前品种列表: 107
2025-06-20 16:02:18 - contract_fetcher - INFO - 过滤后品种列表: 107
2025-06-20 16:02:18 - contract_fetcher - INFO - 获取到 943 个未过期的期权合约----用于采集数据
2025-06-20 16:02:18 - contract_fetcher - INFO - 当前小时时间: 16
2025-06-20 16:02:18 - contract_fetcher - INFO - 过滤前品种列表: 943
2025-06-20 16:02:18 - contract_fetcher - INFO - 过滤后品种列表: 943
2025-06-20 16:02:18 - contract_fetcher - INFO - 时间过滤后剩余 943 个期权合约----用于采集数据
