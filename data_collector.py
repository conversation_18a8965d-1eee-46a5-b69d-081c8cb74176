from tqsdk import Tq<PERSON><PERSON>, TqA<PERSON>, TqKq
from datetime import datetime
from contract_fetcher import get_active_contracts,get_active_options
from config import TQ_USER, TQ_PASS,get_code
from db import DB_DIR, get_product_id,get_market_db_path
import os
import sqlite3
import time
import traceback
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import socket
import urllib3
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter
import threading
from queue import Queue
import contextlib
import logging
from logger_config import setup_logger
import gc
import psutil

# 创建日志记录器
logger = setup_logger('data_collector')

class DatabaseManager:
    def __init__(self):
        self.connection_lock = threading.Lock()
        self.thread_local = threading.local()
        self.table_cache = {}  # 缓存已创建的表

    def get_connection(self, db_path):
        """获取当前线程的数据库连接"""
        if not hasattr(self.thread_local, 'connections'):
            self.thread_local.connections = {}
        
        if db_path not in self.thread_local.connections:
            with self.connection_lock:
                self.thread_local.connections[db_path] = sqlite3.connect(
                    db_path, 
                    timeout=30.0,
                    check_same_thread=False
                )
                # 设置WAL模式以提高并发性能
                self.thread_local.connections[db_path].execute("PRAGMA journal_mode=WAL")
                # 优化SQLite性能
                self.thread_local.connections[db_path].execute("PRAGMA synchronous=NORMAL")
                self.thread_local.connections[db_path].execute("PRAGMA cache_size=10000")
                self.thread_local.connections[db_path].execute("PRAGMA temp_store=MEMORY")
        
        return self.thread_local.connections[db_path]

    def close_connections(self):
        """关闭当前线程的所有数据库连接"""
        if hasattr(self.thread_local, 'connections'):
            for conn in self.thread_local.connections.values():
                try:
                    conn.commit()  # 确保数据已提交
                    conn.close()
                except Exception as e:
                    logger.error(f"关闭数据库连接时出错: {e}")
            self.thread_local.connections.clear()

    def cleanup_all_connections(self):
        """清理所有数据库连接"""
        self.close_connections()

    def ensure_table_exists(self, db_path, table_name, create_sql):
        """确保表存在，使用缓存避免重复创建"""
        cache_key = f"{db_path}:{table_name}"
        if cache_key not in self.table_cache:
            try:
                conn = self.get_connection(db_path)
                conn.execute(create_sql)
                conn.commit()
                self.table_cache[cache_key] = True
                logger.debug(f"创建表: {table_name}")
            except Exception as e:
                logger.error(f"创建表 {table_name} 时出错: {e}")
                return False
        return True

class DataCollector:
    def __init__(self,H=4,batch_size=200,max_workers=32):  # 保持4小时历史数据
        self.api = None
        self.ticks = {}
        self.H=H  # 保持4小时历史数据，确保数据完整性
        self.klines = {}
        self.running = True
        self.batch_size = batch_size  # 增加批处理大小
        self.max_workers = max_workers  # 增加工作线程数
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        self.max_retries = 2  # 减少重试次数
        self.retry_delay = 1  # 减少重试延迟
        self.db_manager = DatabaseManager()
        self.performance_stats = {
            'start_time': None,
            'contracts_processed': 0,
            'total_time': 0,
            'avg_time_per_contract': 0
        }
        self._log_memory_usage("初始化")

    def _log_memory_usage(self, stage=""):
        """记录内存使用情况"""
        try:
            process = psutil.Process(os.getpid())
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            logger.info(f"内存使用情况 [{stage}]: {memory_mb:.2f} MB")
        except Exception as e:
            logger.warning(f"无法获取内存使用情况: {e}")

    def _cleanup_memory(self):
        """清理内存"""
        try:
            # 强制垃圾回收
            gc.collect()
            
            # 清理数据库连接
            self.db_manager.cleanup_all_connections()
            
            # 清理数据缓存
            self.ticks.clear()
            self.klines.clear()
            
            # 记录清理后的内存使用
            self._log_memory_usage("清理后")
            
        except Exception as e:
            logger.error(f"内存清理时出错: {e}")

    def connect_api(self):
        """连接天勤API，带重试机制"""
        retry_count = 0
        while retry_count < self.max_retries:
            try:
                if self.api:
                    try:
                        self.api.close()
                    except:
                        pass
                    self.api = None
                
                # 创建新的API连接
                self.api = TqApi(TqKq(), auth=TqAuth(TQ_USER, TQ_PASS), web_gui=False)  # 关闭web_gui减少内存
                logger.info("已重新连接天勤API")
                return True
            except Exception as e:
                retry_count += 1
                logger.error(f"连接API失败 (尝试 {retry_count}/{self.max_retries}): {e}")
                if retry_count < self.max_retries:
                    logger.info(f"等待 {self.retry_delay} 秒后重试...")
                    time.sleep(self.retry_delay)
                else:
                    logger.error("达到最大重试次数，放弃连接")
                    return False

    def collect_data(self, qh="期货", contracts=[]):
        """采集数据 - 优化版本"""
        try:
            start_time = time.time()
            logger.info(f"开始更新{qh}数据... 合约数量: {len(contracts)}")
            self._log_memory_usage(f"开始采集{qh}")
            
            if not self.connect_api():
                logger.warning("无法建立API连接，跳过本次数据采集")
                return

            # 预创建所有需要的表，避免在线程中重复创建
            self._pre_create_tables(contracts, qh)
            
            # 分批处理合约，使用更大的批次
            batch_size = min(self.batch_size, len(contracts))
            total_batches = (len(contracts) + batch_size - 1) // batch_size
            
            logger.info(f"将分 {total_batches} 批处理，每批 {batch_size} 个合约")
            
            for batch_num in range(0, len(contracts), batch_size):
                batch_start = time.time()
                batch_contracts = contracts[batch_num:batch_num + batch_size]
                current_batch = batch_num // batch_size + 1
                
                logger.info(f"处理第 {current_batch}/{total_batches} 批，合约数量: {len(batch_contracts)}")
                
                # 使用线程池并行处理合约
                futures = []
                for symbol in batch_contracts:
                    future = self.executor.submit(self.process_contract, symbol, qh)
                    futures.append(future)
                
                # 等待当前批次完成，使用as_completed提高效率
                completed_count = 0
                for future in as_completed(futures):
                    try:
                        result = future.result(timeout=180)  # 3分钟超时
                        if result:
                            completed_count += 1
                            self.performance_stats['contracts_processed'] += 1
                    except Exception as e:
                        logger.error(f"处理合约时出错: {e}")
                
                batch_time = time.time() - batch_start
                logger.info(f"第 {current_batch} 批完成，耗时: {batch_time:.2f}秒，成功处理: {completed_count}/{len(batch_contracts)}")
                
                # 批次完成后清理内存
                self._cleanup_memory()

            total_time = time.time() - start_time
            self.performance_stats['total_time'] = total_time
            if self.performance_stats['contracts_processed'] > 0:
                self.performance_stats['avg_time_per_contract'] = total_time / self.performance_stats['contracts_processed']
            
            logger.info(f"{qh}数据采集完成，总耗时: {total_time:.2f}秒，平均每合约: {self.performance_stats['avg_time_per_contract']:.2f}秒")

        except Exception as e:
            logger.error(f"采集{qh}数据时出错: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
        finally:
            if self.api:
                try:
                    self.api.close()
                except:
                    pass
                self.api = None
                logger.info("已关闭API连接")
            
            # 最终清理
            self._cleanup_memory()

    def _pre_create_tables(self, contracts, qh):
        """预创建所有需要的表，避免在线程中重复创建"""
        logger.info(f"预创建 {qh} 数据表...")
        tables_created = 0
        
        for symbol in contracts:
            try:
                db_path = get_market_db_path(symbol)
                table_name = f"klines_{symbol.replace('.', '_').replace('-', '_').replace('+', '_')}"
                
                create_sql = f'''
                    CREATE TABLE IF NOT EXISTS {table_name} (
                        id INTEGER,
                        datetime INTEGER,
                        open REAL,
                        high REAL,
                        low REAL,
                        close REAL,
                        volume INTEGER,
                        open_oi INTEGER,
                        close_oi INTEGER,
                        PRIMARY KEY (id)
                    )
                '''
                
                if self.db_manager.ensure_table_exists(db_path, table_name, create_sql):
                    tables_created += 1
                    
            except Exception as e:
                logger.warning(f"预创建表 {symbol} 时出错: {e}")
        
        logger.info(f"预创建了 {tables_created} 个表")

    def process_contract(self, symbol, qh):
        """处理单个合约的数据 - 优化版本"""
        api = None
        retry_count = 0
        
        while retry_count < self.max_retries:
            try:
                # 为每个线程创建独立的API连接
                api = TqApi(TqKq(), auth=TqAuth(TQ_USER, TQ_PASS), web_gui=False)
                
                # 保持4小时历史数据，确保数据完整性
                data_length = 6 * 60 * self.H  # 保持1440条数据，确保4小时完整覆盖
                kline = api.get_kline_serial(symbol, duration_seconds=10, data_length=data_length)
                
                if len(kline) > 0:
                    # 使用批量插入提高数据库性能
                    self.save_kline_data_batch(symbol, kline, qh)
                    logger.debug(f"成功处理{qh} {symbol}，数据量: {len(kline)}")
                    return True
                else:
                    logger.warning(f"{qh} {symbol} 无数据")
                    return False

            except Exception as e:
                retry_count += 1
                logger.error(f"处理{qh} {symbol} 数据失败 (尝试 {retry_count}/{self.max_retries}): {e}")
                
                if retry_count < self.max_retries:
                    logger.info(f"等待 {self.retry_delay} 秒后重试...")
                    time.sleep(self.retry_delay)
                else:
                    logger.error(f"处理{qh} {symbol} 数据达到最大重试次数，跳过该合约")
                    return False
            finally:
                # 确保API连接被关闭
                if api:
                    try:
                        api.close()
                    except:
                        pass
                
                # 关闭当前线程的数据库连接
                self.db_manager.close_connections()

    def save_kline_data_batch(self, symbol, kline, qh):
        """批量保存K线数据 - 优化版本"""
        try:
            db_path = get_market_db_path(symbol)
            conn = self.db_manager.get_connection(db_path)
            
            # 处理表名中的特殊字符
            table_name = f"klines_{symbol.replace('.', '_').replace('-', '_').replace('+', '_')}"
            
            # 准备批量数据
            kline_data = []
            for i in range(len(kline)):
                try:
                    kline_data.append((
                        kline.iloc[i].id,
                        kline.iloc[i].datetime,
                        kline.iloc[i].open,
                        kline.iloc[i].high,
                        kline.iloc[i].low,
                        kline.iloc[i].close,
                        kline.iloc[i].volume,
                        kline.iloc[i].open_oi,
                        kline.iloc[i].close_oi
                    ))
                except Exception as e:
                    logger.error(f"处理第{i}条K线数据时出错: {e}")
                    continue

            if kline_data:
                try:
                    # 使用批量插入提高性能
                    c = conn.cursor()
                    c.executemany(f'''
                        INSERT OR REPLACE INTO {table_name}
                        (id, datetime, open, high, low, close,
                        volume, open_oi, close_oi)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    ''', kline_data)
                    conn.commit()
                    logger.debug(f"成功保存 {len(kline_data)} 条K线数据到 {table_name}")
                except Exception as e:
                    logger.error(f"保存K线数据到数据库时出错: {e}")
                    conn.rollback()
        except Exception as e:
            logger.error(f"保存K线数据时出错: {e}")

    def 批量处理(self,test=1):
        """批量处理数据采集任务 - 优化版本"""
        try:
            start_time = time.time()
            logger.info("开始批量处理数据采集任务")
            self._log_memory_usage("批量处理开始")
            
            # 重置性能统计
            self.performance_stats = {
                'start_time': start_time,
                'contracts_processed': 0,
                'total_time': 0,
                'avg_time_per_contract': 0
            }
            
            # 获取活跃合约列表
            futures_contracts = get_active_contracts()[-2]
            options_contracts = get_active_options()
            
            total_contracts = len(futures_contracts) + len(options_contracts)
            logger.info(f"总计需要处理 {total_contracts} 个合约（期货: {len(futures_contracts)}, 期权: {len(options_contracts)}）")
            
            # 处理期货合约
            if futures_contracts:
                logger.info(f"开始处理 {len(futures_contracts)} 个期货合约")
                self.collect_data("期货", futures_contracts)
            
            # 处理期权合约
            if options_contracts:
                logger.info(f"开始处理 {len(options_contracts)} 个期权合约")
                self.collect_data("期权", options_contracts)
            
            total_time = time.time() - start_time
            logger.info(f"批量处理数据采集任务完成，总耗时: {total_time:.2f}秒")
            logger.info(f"性能统计: 处理合约数: {self.performance_stats['contracts_processed']}, 平均每合约: {self.performance_stats['avg_time_per_contract']:.2f}秒")
            self._log_memory_usage("批量处理完成")
            
        except Exception as e:
            logger.error(f"批量处理数据采集任务时出错: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
        finally:
            # 最终清理
            self._cleanup_memory()

    def stop(self):
        """停止数据采集"""
        self.running = False
        
        # 关闭线程池
        try:
            self.executor.shutdown(wait=True, timeout=30)
        except Exception as e:
            logger.error(f"关闭线程池时出错: {e}")
        
        # 关闭API连接
        if self.api:
            try:
                self.api.close()
            except:
                pass
            self.api = None
        
        # 清理数据库连接
        self.db_manager.cleanup_all_connections()
        
        # 最终内存清理
        self._cleanup_memory()
        
        logger.info("数据采集已停止")

if __name__ == '__main__':
    collector = DataCollector()
    try:
        print("\n开始测试期货数据采集...")
        collector.批量处理(test=2)
        print("\n期货数据采集测试完成")
    except KeyboardInterrupt:
        print("\n停止数据采集...")
    except Exception as e:
        print(f"测试过程中出错: {e}")
        print(f"错误详情: {traceback.format_exc()}")
    finally:
        collector.stop() 