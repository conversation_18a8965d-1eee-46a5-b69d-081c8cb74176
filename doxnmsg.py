# !/usr/bin/python3
# -*-coding:utf-8-*-
#参考
#https://work.weixin.qq.com/api/doc/90000/90135/90253
#https://work.weixin.qq.com/api/doc/90000/90135/90236
#https://blog.csdn.net/itanping/article/details/*********
# https://developer.work.weixin.qq.com/document/path/90236

import os
import json
import time
import requests
from typing import Optional, Dict, Union

class wxmsg:

    def __init__(self, app=1):

        if app == 1:
            # 应用ID
            self.AgentId = '1000004'  #弱水三千
            # 应用Secret
            self.Secret = "_GqQPKU_g2ArPrFz9NPzMerQSHUQsRYF7FhXTnsT1Wo"  #'u_ZMXuKvrLyJX_9_cJplXhUXdGftXI6-tEcwjO9hZbI'
            # 企业ID
            self.CompanyId = 'ww015cc8938c184bf6'
        elif app == 2:  #
            self.AgentId = '1000006'
            self.Secret = 'TZFbnA7aaWh2765GM7tcWXOtJmKaX01bWQzLKXVwj0o'
            self.CompanyId = 'ww015cc8938c184bf6'
        elif app == 3:
            self.AgentId = '1000005'  #信息转发
            self.Secret = 'u_ZMXuKvrLyJX_9_cJplXhUXdGftXI6-tEcwjO9hZbI'
            self.CompanyId = 'ww015cc8938c184bf6'
        else:
            self.AgentId = '1000003'  #搬砖仙人
            self.Secret = 'u33Cm2rSV5HSiqdVw01ENELUeVOppCWqlGhERgckgOo'
            self.CompanyId = 'ww015cc8938c184bf6'

        # 通行密钥
        r = requests.post(
            f'https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={self.CompanyId}&corpsecret={self.Secret}').json()
        self.ACCESS_TOKEN = r["access_token"]

    def text(self, message, user="WangQi"):
        '''"content" : "你的快递已到，请携带工卡前往邮件中心领取。\n出发前可查看<a href=\"http://work.weixin.qq.com\">邮件中心视频实况</a>，聪明避开排队。"'''
        # 要发送的信息格式
        data = {
            "touser": f"{user}",
            "msgtype": "text",  #text
            "agentid": f"{self.AgentId}",
            "text": {
                "content": f"{message}"
            }
        }
        # 字典转成json，不然会报错
        data = json.dumps(data)
        # 发送消息
        r = requests.post(f'https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={self.ACCESS_TOKEN}',
                          data=data)
        if r.status_code == 200:
            data001 = r.json()
            if data001["errmsg"] != "ok":
                print(data001)
                r = requests.post(
                    f'https://qyapi.weixin.qq.com/cgi-bin/gettoken?corpid={self.CompanyId}&corpsecret={self.Secret}'
                ).json()
                self.ACCESS_TOKEN = r["access_token"]
                requests.post(f'https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={self.ACCESS_TOKEN}',
                              data=data)

    def textcard(self, title="标题", content="textcard", url="", btntxt="...", user="WangQi"):
        ''''''
        data = {
            "touser": f"{user}",
            "msgtype": "textcard",  #text
            "agentid": f"{self.AgentId}",
            "textcard": {
                "title": f"{title}",
                #描述，不超过512个字节，超过会自动截断（支持id转译）
                "description": f"{content}",
                "url": f"{url}",
                "btntxt": f"{btntxt}"
            },
        }
        # 字典转成json，不然会报错
        data = json.dumps(data)
        # 发送消息
        requests.post(f'https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={self.ACCESS_TOKEN}', data=data)
        #print(r.json())

    def news(self, title="标题", content="textcard", url="", picurl="", user="WangQi"):
        # 要发送的信息格式
        data = {
            "touser": f"{user}",
            "msgtype": "news",  #text
            "agentid": f"{self.AgentId}",
            "news": {
                "articles": [{
                    "title": f"{title}",
                    #描述，不超过512个字节，超过会自动截断（支持id转译）
                    "description": f"{content}",
                    "url": f"{url}",
                    "picurl": f"{picurl}"
                }]
            }
        }
        # 字典转成json，不然会报错
        data = json.dumps(data)
        # 发送消息
        requests.post(f'https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={self.ACCESS_TOKEN}', data=data)
        #print(r.json())

    def md(self, message, user="WangQi"):
        # 要发送的信息格式
        data = {
            "touser": f"{user}",
            "msgtype": "markdown",  #text
            "toall": 0,
            "agentid": f"{self.AgentId}",
            "markdown": {
                "content": f"{message}"
            }
        }
        # 字典转成json，不然会报错
        data = json.dumps(data)
        # 发送消息
        requests.post(f'https://qyapi.weixin.qq.com/cgi-bin/message/send?access_token={self.ACCESS_TOKEN}', data=data)
        # print(r.json())


import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
from email.mime.application import MIMEApplication


class email:

    def __init__(self, app=1):
        # 邮件发送配置
        self.sender_email = '<EMAIL>'
        self.sender_password = 'djxtjrvyxxwaecgg'
        self.receiver_email = '<EMAIL>'  #bfitytqpmnnwbchc

    def fasong(self, subject='邮件主题', body='邮件正文内容', filename='附件', fj_path=""):
        # 创建邮件对象
        msg = MIMEMultipart()
        msg['From'] = self.sender_email
        msg['To'] = self.receiver_email
        msg['Subject'] = subject

        # 邮件正文部分
        msg.attach(MIMEText(body, 'html'))
        print("发送email")

        # 附件部分
        if not os.path.exists(fj_path):
            print(f'文件 {fj_path} 不存在，请确保文件已正确保存。')
        else:
            with open(fj_path, 'rb') as file:
                part = MIMEApplication(file.read(), 'html')
                # part.add_header('Content-Disposition', f'attachment; filename= {filename}')
                part.add_header('Content-Disposition', 'attachment', filename=f'{filename}')
                msg.attach(part)
        # 发送邮件
        try:
            server = smtplib.SMTP_SSL('smtp.qq.com', 465)  # 使用QQ邮箱的SMTP服务器
            server.login(self.sender_email, self.sender_password)  # 登录邮箱
            text = msg.as_string()
            server.sendmail(self.sender_email, self.receiver_email, text)
            server.quit()
            print('邮件发送成功')
        except Exception as e:
            print(f'邮件发送失败: {e}')

class gotifymsg:
    def __init__(self, server_url="", token="app"):
        '''
        示例调用
        msg=appmsg()
        xx=msg.send( title="测试", message="```\nCPU使用率: 95%\n内存剩余: 20%\n```\n\n222222\n555 启用Markdown渲染(@ref)   启用Markdown渲染(@ref)    5\n[查看详情](http://monitor.example.com)", lv=9)
        print(xx)
        '''
        self.server_url = server_url
        self.server_url="https://nnzhqosjolyg.ap-northeast-1.clawcloudrun.com"
        if token=="bili":
            self.token="A3MNEFlgybFOZMg"
        elif token=="tq":
            self.token="AEmpHwJzHTyGdOu"
        elif token=="nas":
            self.token="AYkgbn-YnTgH5_j"
        else:
            self.token="AHnnG2Z6k6FkHv1"
  

    def send(self, title=" ", message=" ", lv=5):
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.token}"  # 使用Bearer Token认证[1](@ref)
        }
        data = {
            "title": title,
            "message": message,
            "priority": lv,  # 优先级（1-10，数值越大通知越显眼）
            "extras": {
            "client::display": {"contentType": "text/markdown"}  # 启用Markdown渲染[5](@ref)
        }
        }

        response = requests.post(
            f"{self.server_url}/message",
            headers=headers,
            json=data
        )
        return response.json()




class pushmemsg:
    def __init__(self, server_url="http://139.196.48.80:3010",push_key="s6DFHJfoaXZy7bjuSeOO"):
        '''

        '''
        self.server_url = server_url
        if not push_key:
            raise ValueError("PUSHME_KEY未设置")
        self.push_key = push_key

    def send(
        self,
        title: str,
        content: str,
        msg_type: Union[str, str] = "text",
        lv: str="",
        lb: str=""
    ) -> Dict:
        """
        发送消息到PushMe服务
        :param title: 消息标题
        :param content: 消息内容（支持纯文本/Markdown）
        :param msg_type: 消息类型，默认为"text"|"markdown"|"html"|"data"|"markdata"|"chart"|"echarts"
        """

        if  lv!="":
            title=f"[{lv}]{title}"
        if  lb == "" and lv != "":
            if lv =="i":
                lb="一般信息"
            elif lv =="w":
                lb="警告信息"
            elif lv =="f":
                lb="错误信息"
            elif lv =="s":
                lb="成功信息"
        if  lb != "":
            title=f"{title}[~{lb}]"
            print(title)
        params = {"push_key": self.push_key, "title": title, "content": content ,"type":msg_type}

        try:
            response = requests.post(
                self.server_url,
                data=params,
                timeout=5  # 设置5秒超时
            )
            return response
        except requests.exceptions.Timeout:
            print("请求超时")
            raise
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            raise

# ttt='''## P'''
# pushme = pushmemsg()
# pushme.send(title="测试", content=ttt, msg_type="markdown",lv="f",lb="下单信息")

# import requests






# wxmsg = wxmsg(3)
# wxmsg.text('122')

# wxmsg=wxmsg()
# wxmsg.text('你的快递已到，请携带工卡前往邮件中心领取。\n出发前可查看<a href=\"http://yimu://api/addbill?parentCategory=父分类&childCategory=子分类&money=100&time=2023-02-23&remark=备注&asset=账户&bookName=日常账本&tags=标签1">邮件中心视频实况</a>，聪明避开排队。')
# wxmsg = wxmsg()
# wxmsg.md('''####
# >趋势:<font color="blue">用户反馈</font> \n
# >RSI:⤵️<font color="comment">117</font>  <font color="comment">117</font>\n
# >操作建议:<font color=\"comment\">15例</font>"
# <font  color="blue">绿色</font>
# <font  color="red">绿色</font>
# <font  color="green">绿色</font>  info
# 1.  `<font  color="info">绿色</font>`
# 2.  `<font  color="comment">灰色</font>`
# 3.  `<font  color="warning">橙红色</font>`
# ''')

# email1=email()
# email1.fasong(subject='邮件主题',body='邮件正文内容',filename = '附件',fj_path="")
# pip install serverchan-sdk
# from serverchan_sdk import sc_send

# response = sc_send("sctp3281t9s8ftcmjo17ythrrvcu4yr", "title", "desp", {"tags": "服务器报警|图片"})
# print(response)
# pip install pypushdeer
# from pypushdeer import PushDeer

# pushdeer = PushDeer(pushkey="PDU32947TCeXtdteFaFGEvrPcbjnLuO8QCB03IAwQ")
# pushdeer.send_text("hello world", desp="optional description")
# pushdeer.send_markdown("# hello world",
#                        desp='''**optional** descn

#                        arkdown''')
