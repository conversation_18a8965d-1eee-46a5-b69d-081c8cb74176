import os
import sqlite3
from db import get_market_db_path, DB_DIR


def migrate_klines_tables():
    """
    遍历data目录下所有db文件，将所有以klines_开头的表，
    按表名第3个元素为product_id，调用get_market_db_path(product_id)得到目标数据库路径，
    并将表结构和数据迁移到目标数据库，表名不变。
    """
    for root, dirs, files in os.walk(DB_DIR):
        for file in files:
            if file.endswith('.db'):
                src_db_path = os.path.join(root, file)
                print(f"处理数据库: {src_db_path}")
                src_conn = sqlite3.connect(src_db_path)
                src_cursor = src_conn.cursor()
                # 获取所有klines_开头的表
                src_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE 'klines_%'")
                tables = [row[0] for row in src_cursor.fetchall()]
                for table in tables:
                    parts = table.split('_')
                    if len(parts) >= 3:
                        product_id = parts[2]  # 取第3个元素
                        # 获取目标数据库路径
                        dst_db_path = get_market_db_path(product_id)
                        print(f"  迁移表 {table} 到 {dst_db_path}")
                        # 获取表结构
                        src_cursor.execute(f"SELECT sql FROM sqlite_master WHERE type='table' AND name='{table}'")
                        create_sql = src_cursor.fetchone()[0]
                        # 获取所有数据
                        src_cursor.execute(f"SELECT * FROM {table}")
                        rows = src_cursor.fetchall()
                        # 获取列名
                        src_cursor.execute(f"PRAGMA table_info({table})")
                        columns = [info[1] for info in src_cursor.fetchall()]
                        columns_str = ','.join(columns)
                        placeholders = ','.join(['?'] * len(columns))
                        # 在目标数据库创建表（如果不存在）并插入数据
                        dst_conn = sqlite3.connect(dst_db_path)
                        dst_cursor = dst_conn.cursor()
                        # 检查目标数据库是否已存在该表
                        dst_cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table,))
                        exists = dst_cursor.fetchone()
                        if not exists:
                            dst_cursor.execute(create_sql)
                        if rows:
                            dst_cursor.executemany(
                                f"INSERT OR REPLACE INTO {table} ({columns_str}) VALUES ({placeholders})",
                                rows
                            )
                        dst_conn.commit()
                        dst_conn.close()
                src_conn.close()

if __name__ == "__main__":
    migrate_klines_tables()
    print("全部迁移完成！") 