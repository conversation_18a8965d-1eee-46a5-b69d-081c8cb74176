from data_collector import DataCollector
from contract_fetcher import fetch_and_save_contracts,fetch_and_save_options, get_active_contracts,get_active_options
from db import init_all_dbs, CONTRACTS_DB
from doxnmsg import pushmemsg
import time
from datetime import datetime, timedelta
import sqlite3
import os
import schedule
import traceback
import pytz
import logging
from logging.handlers import RotatingFileHandler
from logger_config import setup_logger
import gc
import psutil
import argparse

set_H=4
set_batch_size=50
set_max_workers=8

# 创建日志记录器
logger = setup_logger('data_collector')

def get_local_time():
    """获取本地时间"""
    # 获取系统时区
    local_tz = pytz.timezone('Asia/Shanghai')
    return datetime.now(local_tz)

def is_trading_time() -> bool:
    """检查当前是否为交易时间"""
    now = get_local_time()  # 使用本地时间
    current_time = now.time()
    weekday = now.weekday()
    
    # 周末不交易
    if weekday > 5 :  # 5=周六, 6=周日
        return False

    # 定义交易时间段（24小时制）
    trading_periods = [
        # 上午交易时段
        (datetime.strptime("09:00:00", "%H:%M:%S").time(),
         datetime.strptime("11:40:00", "%H:%M:%S").time()),
        # 下午交易时段
        (datetime.strptime("13:00:00", "%H:%M:%S").time(),
         datetime.strptime("15:10:00", "%H:%M:%S").time()),
        # 夜盘交易时段（跨日）
        (datetime.strptime("21:00:00", "%H:%M:%S").time(),
         datetime.strptime("02:40:00", "%H:%M:%S").time())
    ]
    
    # 检查是否在交易时段内
    for start, end in trading_periods:
        # 处理跨日的情况（夜盘）
        if start > end:  # 夜盘跨日
            if current_time >= start or current_time <= end:
                return True
        else:  # 日盘
            if start <= current_time <= end:
                return True
    
    return False

def send_msg(msgtext: str, lv=""):
    """发送消息通知
    Args:
        msgtext: 消息内容
        lv: 消息级别 (i: 信息, w: 警告, f: 错误, s: 成功)
    """
    max_retries = 3  # 最大重试次数
    timeout = 5  # 超时时间（秒）
    
    for attempt in range(max_retries):
        try:
            pushme = pushmemsg()
            response = pushme.send(
                title=f"数据采集程序{lv}",
                content=f"```\n{msgtext}\n```",
                msg_type="markdown",
                lv=lv
            )
            if response.text == "success":
                logger.info("消息发送成功")
                return
            else:
                logger.warning(f"消息发送失败，尝试重试 ({attempt + 1}/{max_retries})")
                time.sleep(2)  # 等待1秒后重试
        except Exception as e:
            logger.error(f"发送消息失败 ({attempt + 1}/{max_retries}): {e}")
            if attempt < max_retries - 1:  # 如果不是最后一次尝试
                time.sleep(2)  # 等待1秒后重试
            else:
                logger.error("消息发送失败，已达到最大重试次数")

def update_contracts():
    """更新合约信息"""
    logger.info(f"开始更新合约信息 - {datetime.now()}")
    send_msg("开始更新合约信息", "i")
    try:
        fetch_and_save_contracts()
        fetch_and_save_options()
        logger.info("合约信息更新完成")
        send_msg("合约信息更新完成", "s")
    except Exception as e:
        error_msg = f"更新合约信息时出错: {e}\n{traceback.format_exc()}"
        logger.error(error_msg)
        send_msg(error_msg, "f")

def get_memory_usage():
    """获取当前内存使用情况"""
    try:
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        return memory_mb
    except Exception as e:
        logger.warning(f"无法获取内存使用情况: {e}")
        return 0

def cleanup_memory():
    """清理内存"""
    try:
        # 强制垃圾回收
        gc.collect()
        
        # 记录清理后的内存使用
        memory_mb = get_memory_usage()
        logger.info(f"内存清理完成，当前使用: {memory_mb:.2f} MB")
        
    except Exception as e:
        logger.error(f"内存清理时出错: {e}")

def run_collection_jobs():
    """运行数据采集任务"""
    start_time = datetime.now()
    memory_before = get_memory_usage()
    logger.info(f"开始运行数据采集任务 - {start_time}")
    logger.info(f"任务开始前内存使用: {memory_before:.2f} MB")
    
    try:
        # 1. 初始化数据库
        logger.info("开始初始化数据库...")
        init_all_dbs()
        
        # 2. 获取活跃合约列表
        logger.info("开始获取活跃合约列表...")
        futures_contracts = get_active_contracts()[-2]
        options_contracts = get_active_options()

        # 3. 采集市场数据（首次运行不检查交易时间）
        logger.info("开始采集市场数据...")
        send_msg("开始采集市场数据", "i")
        collect_start = datetime.now()
        collector = DataCollector(H=set_H, batch_size=set_batch_size, max_workers=set_max_workers)
        
        try:
            collector.批量处理()
        finally:
            # 确保收集器被正确关闭
            collector.stop()
            
        collect_time = datetime.now() - collect_start
        logger.info(f"市场数据采集完成，耗时: {collect_time}")
        
        # 生成详细报告
        total_time = datetime.now() - start_time
        memory_after = get_memory_usage()
        memory_diff = memory_after - memory_before
        
        report = f"""数据采集任务完成报告
====================
执行时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}
总耗时: {total_time}

1. 合约信息统计
- 期货合约数量: {len(futures_contracts)}
- 期权相关合约数量: {len(options_contracts)}

2. 市场数据采集
- 采集耗时: {str(collect_time).split('.')[0]}
- 批处理大小: {set_batch_size}
- 最大工作线程: {set_max_workers}
- 历史数据周期: {set_H}小时

3. 内存使用情况
- 任务开始前: {memory_before:.2f} MB
- 任务结束后: {memory_after:.2f} MB
- 内存变化: {memory_diff:+.2f} MB

4. 性能统计
- 数据采集: {str(collect_time).split('.')[0]}
- 总耗时: {str(total_time).split('.')[0]}

任务状态: 成功完成

期货采集：{futures_contracts}
期权采集：{options_contracts}

"""
        logger.info(report)
        send_msg(report, "s")
        
        # 任务完成后清理内存
        cleanup_memory()
        
    except Exception as e:
        error_msg = f"""数据采集任务失败报告
====================
执行时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}
错误信息: {str(e)}
详细堆栈:
{traceback.format_exc()}
"""
        logger.error(error_msg)
        send_msg(error_msg, "f")
        
        # 即使失败也要清理内存
        cleanup_memory()

def schedule_jobs():
    """设置定时任务"""
    local_time = get_local_time()
    logger.info(f"当前系统时间: {local_time}")
    
    # 每天下午2点50分更新合约信息
    schedule.every().day.at("14:50").do(update_contracts)
    logger.info("已设置合约更新任务: 每天14:50")
    
    # 定时采集市场数据（仅在交易时间内）
    def scheduled_collect():
        if is_trading_time():
            logger.info(f"开始执行定时采集任务 - {get_local_time()}")
            run_collection_jobs()
        else:
            logger.info(f"非交易时间，跳过采集 - {get_local_time()}")

    # 设置定时任务 - 优化时间点，确保在交易时间内
    schedule_times = [
        "11:35",   # 上午11点
        "15:05",   # 下午2点
        "23:05",   # 夜盘11点
        "01:35",   # 夜盘11点
        "02:35"    # 凌晨2点
    ]
    
    for time_str in schedule_times:
        schedule.every().day.at(time_str).do(scheduled_collect)
        logger.info(f"已设置数据采集任务: 每天{time_str}")

def memory_monitor():
    """内存监控任务"""
    memory_mb = get_memory_usage()
    logger.info(f"内存监控 - 当前使用: {memory_mb:.2f} MB")
    
    # 如果内存使用超过1GB，进行清理
    if memory_mb > 1024:
        logger.warning(f"内存使用过高 ({memory_mb:.2f} MB)，执行清理...")
        cleanup_memory()

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="交易数据采集主程序")
    parser.add_argument('--once', action='store_true', help='只采集一次数据后退出')
    parser.add_argument('--update-contracts', action='store_true', help='只更新合约信息后退出')
    args = parser.parse_args()

    if args.update_contracts:
        logger.info("只更新合约信息后退出...")
        update_contracts()
        return
    if args.once:
        logger.info("只采集一次数据后退出...")
        run_collection_jobs()
        return

    # 默认定时任务模式
    try:
        schedule_jobs()
        schedule.every().hour.do(memory_monitor)
        logger.info("已设置内存监控任务: 每小时执行一次")
        logger.info("定时任务已启动，等待执行...")
        logger.info(f"程序启动时内存使用: {get_memory_usage():.2f} MB")
        while True:
            schedule.run_pending()
            time.sleep(1)
    except Exception as e:
        error_msg = f"程序运行出错: {e}\n{traceback.format_exc()}"
        logger.error(error_msg)
        send_msg(error_msg, "f")

if __name__ == "__main__":
    main() 