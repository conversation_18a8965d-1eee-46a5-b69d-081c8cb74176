#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能测试脚本 - 测试优化后的数据采集性能
"""

import time
import psutil
import os
from data_collector import DataCollector
from contract_fetcher import get_active_contracts, get_active_options
from logger_config import setup_logger

# 创建日志记录器
logger = setup_logger('performance_test')

def get_memory_usage():
    """获取当前进程内存使用情况"""
    try:
        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()
        return memory_info.rss / 1024 / 1024  # MB
    except Exception as e:
        logger.error(f"获取内存使用情况失败: {e}")
        return 0

def test_performance():
    """测试数据采集性能"""
    print("=" * 60)
    print("🚀 数据采集性能测试")
    print("=" * 60)
    
    # 测试不同配置的性能
    test_configs = [
        {"H": 1, "batch_size": 50, "max_workers": 8, "name": "标准配置"},
        {"H": 2, "batch_size": 100, "max_workers": 16, "name": "优化配置"},
        {"H": 1, "batch_size": 200, "max_workers": 32, "name": "高性能配置"},
    ]
    
    for config in test_configs:
        print(f"\n📊 测试配置: {config['name']}")
        print(f"   历史数据: {config['H']}小时")
        print(f"   批处理大小: {config['batch_size']}")
        print(f"   工作线程: {config['max_workers']}")
        print("-" * 40)
        
        # 创建采集器
        collector = DataCollector(
            H=config['H'],
            batch_size=config['batch_size'],
            max_workers=config['max_workers']
        )
        
        try:
            # 记录开始时间和内存
            start_time = time.time()
            start_memory = get_memory_usage()
            
            print(f"开始时间: {time.strftime('%H:%M:%S')}")
            print(f"开始内存: {start_memory:.2f} MB")
            
            # 执行数据采集
            collector.批量处理(test=2)  # 只测试期货
            
            # 记录结束时间和内存
            end_time = time.time()
            end_memory = get_memory_usage()
            total_time = end_time - start_time
            
            # 输出性能统计
            print(f"结束时间: {time.strftime('%H:%M:%S')}")
            print(f"结束内存: {end_memory:.2f} MB")
            print(f"总耗时: {total_time:.2f} 秒")
            print(f"内存变化: {end_memory - start_memory:+.2f} MB")
            
            # 输出详细统计
            if hasattr(collector, 'performance_stats'):
                stats = collector.performance_stats
                if stats['contracts_processed'] > 0:
                    print(f"处理合约数: {stats['contracts_processed']}")
                    print(f"平均每合约: {stats['avg_time_per_contract']:.2f} 秒")
                    print(f"理论总时间: {stats['contracts_processed'] * stats['avg_time_per_contract']:.2f} 秒")
                    print(f"实际总时间: {stats['total_time']:.2f} 秒")
                    print(f"并行效率: {(stats['contracts_processed'] * stats['avg_time_per_contract'] / stats['total_time']):.2f}x")
            
            print(f"✅ {config['name']} 测试完成")
            
        except Exception as e:
            print(f"❌ {config['name']} 测试失败: {e}")
            logger.error(f"测试配置 {config['name']} 失败: {e}")
        finally:
            collector.stop()
            time.sleep(2)  # 等待资源释放

def test_contract_counts():
    """测试合约数量统计"""
    print("\n" + "=" * 60)
    print("📈 合约数量统计")
    print("=" * 60)
    
    try:
        # 获取期货合约
        futures_contracts = get_active_contracts()[-2]
        print(f"期货合约数量: {len(futures_contracts)}")
        
        # 获取期权合约
        options_contracts = get_active_options()
        print(f"期权合约数量: {len(options_contracts)}")
        
        total_contracts = len(futures_contracts) + len(options_contracts)
        print(f"总合约数量: {total_contracts}")
        
        # 按品种统计
        futures_by_product = {}
        for contract in futures_contracts:
            product = contract.split('.')[1][:2]
            product = ''.join(c for c in product if not c.isdigit())
            futures_by_product[product] = futures_by_product.get(product, 0) + 1
        
        print(f"\n期货品种分布:")
        for product, count in sorted(futures_by_product.items()):
            print(f"  {product}: {count} 个合约")
            
    except Exception as e:
        print(f"获取合约信息失败: {e}")
        logger.error(f"获取合约信息失败: {e}")

def estimate_performance():
    """估算性能表现"""
    print("\n" + "=" * 60)
    print("🔮 性能估算")
    print("=" * 60)
    
    try:
        futures_contracts = get_active_contracts()[-2]
        options_contracts = get_active_options()
        total_contracts = len(futures_contracts) + len(options_contracts)
        
        print(f"总合约数量: {total_contracts}")
        
        # 不同配置的性能估算
        configs = [
            {"H": 1, "batch_size": 50, "max_workers": 8, "name": "标准配置"},
            {"H": 2, "batch_size": 100, "max_workers": 16, "name": "优化配置"},
            {"H": 1, "batch_size": 200, "max_workers": 32, "name": "高性能配置"},
        ]
        
        for config in configs:
            # 估算每个合约的处理时间（基于历史数据量）
            data_points = config['H'] * 60 * 2  # 2分钟K线
            estimated_time_per_contract = data_points * 0.01  # 假设每条数据0.01秒
            
            # 考虑并行处理
            parallel_factor = min(config['max_workers'], config['batch_size'])
            estimated_total_time = (total_contracts * estimated_time_per_contract) / parallel_factor
            
            print(f"\n{config['name']}:")
            print(f"  历史数据: {config['H']}小时 ({data_points} 条K线)")
            print(f"  估算每合约: {estimated_time_per_contract:.2f} 秒")
            print(f"  并行因子: {parallel_factor}")
            print(f"  估算总时间: {estimated_total_time:.2f} 秒 ({estimated_total_time/60:.1f} 分钟)")
            
    except Exception as e:
        print(f"性能估算失败: {e}")
        logger.error(f"性能估算失败: {e}")

def main():
    """主函数"""
    print("🚀 启动数据采集性能测试...")
    
    try:
        # 测试合约数量
        test_contract_counts()
        
        # 估算性能
        estimate_performance()
        
        # 询问是否进行实际测试
        print("\n" + "=" * 60)
        response = input("是否进行实际性能测试？(y/n): ").lower().strip()
        
        if response in ['y', 'yes', '是']:
            test_performance()
        else:
            print("跳过实际测试，仅显示估算结果")
            
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试过程中出错: {e}")
        logger.error(f"测试过程中出错: {e}")
    finally:
        print("\n🏁 性能测试完成")

if __name__ == "__main__":
    main() 