# 期货&期权行情数据采集系统

本项目基于 tqsdk，自动采集全市场期货、期权合约的K线、Tick和盘口（买一/卖一）数据，支持数据存储和自动化采集，便于后续回测和量化分析。

## 功能特点
- 自动获取所有期货、期权合约列表
- 实时采集K线、Tick、盘口（买一/卖一）数据
- 数据存储于SQLite数据库，便于管理和迁移
- 支持定时自动采集，保证数据连续性
- 结构清晰，便于后续扩展和迁移

## 目录结构
```
交易数据采集/
├── main.py                # 入口，调度采集任务
├── contract_fetcher.py    # 合约列表采集
├── data_collector.py      # 行情采集
├── db.py                  # 数据库操作
├── config.py              # 配置文件
├── requirements.txt       # 依赖库
└── README.md              # 项目说明
```

## 依赖安装
```bash
pip install -r requirements.txt
```

## 数据库结构
### 1. 合约信息表（contracts）
| 字段名       | 类型     | 说明           |
|--------------|----------|----------------|
| id           | INTEGER  | 主键，自增     |
| symbol       | TEXT     | 合约代码       |
| exchange     | TEXT     | 交易所         |
| name         | TEXT     | 合约名称       |
| type         | TEXT     | 期货/期权      |
| underlying   | TEXT     | 标的（期权用） |
| listed_date  | DATE     | 上市日期       |
| expired_date | DATE     | 到期日         |

### 2. K线数据表（kline_data）
| 字段名        | 类型     | 说明           |
|---------------|----------|----------------|
| id            | INTEGER  | 主键，自增     |
| symbol        | TEXT     | 合约代码       |
| datetime      | DATETIME | K线时间        |
| period        | TEXT     | K线周期        |
| open          | REAL     | 开盘价         |
| high          | REAL     | 最高价         |
| low           | REAL     | 最低价         |
| close         | REAL     | 收盘价         |
| volume        | REAL     | 成交量         |
| open_interest | REAL     | 持仓量         |

### 3. Tick数据表（tick_data）
| 字段名        | 类型     | 说明           |
|---------------|----------|----------------|
| id            | INTEGER  | 主键，自增     |
| symbol        | TEXT     | 合约代码       |
| datetime      | DATETIME | 时间戳         |
| last_price    | REAL     | 最新价         |
| volume        | REAL     | 成交量         |
| open_interest | REAL     | 持仓量         |

### 4. 盘口数据表（market_depth）
| 字段名        | 类型     | 说明           |
|---------------|----------|----------------|
| id            | INTEGER  | 主键，自增     |
| symbol        | TEXT     | 合约代码       |
| datetime      | DATETIME | 时间戳         |
| last_price    | REAL     | 最新价         |
| volume        | REAL     | 成交量         |
| open_interest | REAL     | 持仓量         |
| bid_price1    | REAL     | 买一价         |
| bid_volume1   | REAL     | 买一量         |
| ask_price1    | REAL     | 卖一价         |
| ask_volume1   | REAL     | 卖一量         |

## 使用方法
1. 配置 `config.py`，设置采集周期、数据库路径等参数
2. 运行 `main.py` 启动采集

## 常见问题
- 数据量大时可平滑迁移到MySQL/PostgreSQL
- 采集频率、合约范围可在配置文件中调整

## 许可证
MIT License 

## 项目概述
这是一个自动化的交易数据采集系统，用于定时采集期货和期权市场数据。

## 主要功能
1. **合约信息更新**：自动更新期货和期权合约信息
2. **市场数据采集**：定时采集市场数据
3. **交易时间判断**：智能判断当前是否为交易时间
4. **消息通知**：通过消息推送通知任务执行状态
5. **内存监控**：实时监控内存使用情况，防止内存泄漏

## 修复的问题

### 1. 定时任务未启用
**问题**：在 `main()` 函数中，`schedule_jobs()` 被注释掉了，导致定时任务无法设置。

**修复**：
```python
# 修复前
# schedule_jobs()

# 修复后
schedule_jobs()  # 启用定时任务设置
```

### 2. 时间检查逻辑错误
**问题**：
- 使用了 `datetime.now()` 而不是本地时间
- 夜盘交易时间判断逻辑有误
- 周末判断不完整

**修复**：
```python
def is_trading_time() -> bool:
    now = get_local_time()  # 使用本地时间
    # 添加周末判断
    if weekday >= 5:  # 5=周六, 6=周日
        return False
    # 修复夜盘时间判断逻辑
```

### 3. 内存泄漏问题 ⚠️ **重要修复**
**问题**：程序运行后内存占用越来越大（2.9G），无法释放。

**原因分析**：
- 数据库连接未正确关闭
- TqApi 连接累积
- 线程池资源未释放
- 数据对象累积
- 定时任务循环累积内存

**修复方案**：
```python
# 1. 改进数据库连接管理
class DatabaseManager:
    def cleanup_all_connections(self):
        """清理所有数据库连接"""
        self.close_connections()

# 2. 确保API连接正确关闭
def process_contract(self, symbol, qh):
    api = None
    try:
        api = TqApi(TqKq(), auth=TqAuth(TQ_USER, TQ_PASS), web_gui=False)
        # 处理数据...
    finally:
        if api:
            try:
                api.close()
            except:
                pass

# 3. 添加内存清理机制
def _cleanup_memory(self):
    """清理内存"""
    gc.collect()  # 强制垃圾回收
    self.db_manager.cleanup_all_connections()
    self.ticks.clear()
    self.klines.clear()

# 4. 分批处理避免内存累积
def collect_data(self, qh="期货", contracts=[]):
    batch_size = min(self.batch_size, len(contracts))
    for i in range(0, len(contracts), batch_size):
        batch_contracts = contracts[i:i+batch_size]
        # 处理当前批次...
        self._cleanup_memory()  # 批次完成后清理
```

### 4. 定时任务时间设置优化
**问题**：原设置的时间点可能与交易时间不匹配

**修复**：重新设置更合理的时间点：
- 11:35 - 上午交易时段
- 15:05 - 下午交易时段
- 23:05, 01:35, 02:35 - 夜盘时段

## 使用方法

### 1. 快速启动（推荐）
```bash
# 使用优化配置快速采集
python quick_start.py

# 或者指定模式
python quick_start.py collect    # 快速采集
python quick_start.py test       # 快速测试
python quick_start.py config     # 查看配置
```

### 2. 运行主程序
```bash
python main.py
```

### 3. 测试定时任务
```bash
python test_schedule.py
```

### 4. 性能测试
```bash
# 测试不同配置的性能表现
python performance_test.py
```

### 3. 监控内存使用
```bash
# 监控指定进程
python memory_monitor.py --pid 193431

# 监控包含特定名称的进程
python memory_monitor.py --name main.py

# 列出所有Python进程
python memory_monitor.py --list

# 设置监控间隔和时长
python memory_monitor.py --pid 193431 --interval 30 --duration 3600
```

### 4. 重启服务
```bash
# 给脚本执行权限
chmod +x restart_service.sh

# 重启服务
sudo ./restart_service.sh
```

### 5. 查看日志
程序会自动记录详细的执行日志，包括：
- 任务执行时间
- 数据采集状态
- 内存使用情况
- 错误信息
- 性能统计

## 内存监控功能

### 自动监控
- 每小时自动检查内存使用情况
- 超过1GB时自动清理内存
- 记录内存使用变化

### 手动监控
使用 `memory_monitor.py` 脚本：
```bash
# 实时监控内存使用
python memory_monitor.py --pid <进程ID> --interval 10

# 监控1小时
python memory_monitor.py --pid <进程ID> --duration 3600
```

## 交易时间设置

### 日盘交易时间
- 上午：09:00 - 11:40
- 下午：13:00 - 15:10

### 夜盘交易时间
- 21:00 - 次日 02:40

### 非交易时间
- 周末（周六、周日）
- 午休时间（11:40 - 13:00）
- 日盘收盘后到夜盘开盘前（15:10 - 21:00）

## 配置参数

### 原始配置
```python
set_H = 4              # 历史数据周期（小时）
set_batch_size = 50    # 批处理大小
set_max_workers = 8    # 最大工作线程数
```

### 优化配置（推荐）
```python
HISTORY_HOURS = 1      # 历史数据周期（小时）- 从4小时减少到1小时
BATCH_SIZE = 200       # 批处理大小 - 从50增加到200
MAX_WORKERS = 32       # 最大工作线程数 - 从8增加到32
MAX_RETRIES = 2        # 最大重试次数 - 从3减少到2
RETRY_DELAY = 1        # 重试延迟（秒）- 从5减少到1
```

### 性能提升预期
- **采集时间**：从30分钟减少到5-10分钟
- **速度提升**：3-6倍
- **内存使用**：减少50%以上
- **稳定性**：显著提升

## 注意事项

1. **时区设置**：程序使用上海时区（Asia/Shanghai）
2. **数据库初始化**：首次运行会自动初始化数据库
3. **错误处理**：程序包含完善的错误处理和重试机制
4. **消息通知**：重要事件会通过消息推送通知
5. **内存管理**：程序会自动清理内存，防止内存泄漏

## 故障排除

### 性能问题
1. **采集速度慢**：使用 `python quick_start.py` 启动优化版本
2. **内存使用过高**：检查 `config_optimized.py` 中的内存清理设置
3. **网络超时**：调整 `REQUEST_TIMEOUT` 参数

### 定时任务不执行
1. 检查 `schedule_jobs()` 是否被调用
2. 确认当前时间是否为交易时间
3. 查看日志文件确认任务设置状态

### 时间判断错误
1. 检查系统时区设置
2. 确认 `pytz` 库是否正确安装
3. 验证交易时间配置是否正确

### 内存使用过高
1. 检查程序是否正常清理内存
2. 使用 `memory_monitor.py` 监控内存使用
3. 重启服务释放内存
4. 检查是否有其他程序占用内存

### 数据库锁定错误
1. 检查数据库连接是否正确关闭
2. 确认没有多个进程同时访问同一数据库
3. 重启服务清理数据库连接

## 依赖库
```
schedule
pytz
sqlite3
logging
datetime
psutil
gc
```

## 系统服务管理

### 查看服务状态
```bash
sudo systemctl status qhdatacj
```

### 重启服务
```bash
sudo systemctl restart qhdatacj
```

### 查看日志
```bash
sudo journalctl -u qhdatacj -f
```

## 更新日志
- 2024-01-XX：修复定时任务未启用问题
- 2024-01-XX：优化交易时间判断逻辑
- 2024-01-XX：重新设置定时任务时间点
- 2024-01-XX：**重要** - 修复内存泄漏问题
- 2024-01-XX：添加内存监控功能
- 2024-01-XX：优化数据库连接管理 