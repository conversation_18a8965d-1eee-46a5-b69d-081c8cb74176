#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版数据采集器 - 连接池优化方案
主要优化点：
1. API连接池管理，避免频繁连接建立
2. 数据库连接池，解决并发写入问题
3. 智能重试机制，根据错误类型调整策略
4. 性能监控指标收集
"""

from tqsdk import TqApi, TqAuth, TqKq
from datetime import datetime
from contract_fetcher import get_active_contracts, get_active_options
from config import TQ_USER, TQ_PASS, get_code
from db import DB_DIR, get_product_id, get_market_db_path
import sqlite3
import threading
import time
import traceback
from concurrent.futures import ThreadPoolExecutor, as_completed
import queue
from logger_config import setup_logger
import gc
import psutil
import os
from contextlib import contextmanager
from dataclasses import dataclass
from typing import Optional, Dict, List
import json

# 创建日志记录器
logger = setup_logger('data_collector_optimized')

@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    start_time: datetime
    contracts_processed: int = 0
    total_time: float = 0.0
    avg_time_per_contract: float = 0.0
    connection_reuse_count: int = 0
    db_write_count: int = 0
    error_count: int = 0
    retry_count: int = 0
    memory_usage_mb: float = 0.0

class APIConnectionPool:
    """API连接池管理器"""
    
    def __init__(self, pool_size: int = 3, max_idle_time: int = 300):
        self.pool_size = pool_size
        self.max_idle_time = max_idle_time
        self.connections = queue.Queue(maxsize=pool_size)
        self.connection_times = {}
        self.lock = threading.Lock()
        self.total_connections_created = 0
        self.connection_reuse_count = 0
        
    def _create_connection(self) -> TqApi:
        """创建新的API连接"""
        try:
            api = TqApi(TqKq(), auth=TqAuth(TQ_USER, TQ_PASS), web_gui=False)
            self.total_connections_created += 1
            logger.debug(f"创建新API连接，总计: {self.total_connections_created}")
            return api
        except Exception as e:
            logger.error(f"创建API连接失败: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """获取连接的上下文管理器"""
        api = None
        try:
            # 尝试从池中获取连接
            try:
                api = self.connections.get_nowait()
                self.connection_reuse_count += 1
                logger.debug(f"复用API连接，复用次数: {self.connection_reuse_count}")
            except queue.Empty:
                # 池中没有连接，创建新连接
                api = self._create_connection()
            
            yield api
            
        except Exception as e:
            logger.error(f"API连接使用过程中出错: {e}")
            # 连接出错，不放回池中
            if api:
                try:
                    api.close()
                except:
                    pass
            raise
        finally:
            # 将连接放回池中
            if api:
                try:
                    # 检查连接是否仍然有效
                    if hasattr(api, '_loop') and not api._loop.is_closed():
                        self.connections.put_nowait(api)
                        self.connection_times[id(api)] = time.time()
                    else:
                        api.close()
                except queue.Full:
                    # 池已满，关闭连接
                    try:
                        api.close()
                    except:
                        pass
                except Exception as e:
                    logger.warning(f"放回连接池时出错: {e}")
                    try:
                        api.close()
                    except:
                        pass
    
    def cleanup_idle_connections(self):
        """清理空闲连接"""
        current_time = time.time()
        with self.lock:
            temp_connections = []
            while not self.connections.empty():
                try:
                    api = self.connections.get_nowait()
                    conn_time = self.connection_times.get(id(api), current_time)
                    if current_time - conn_time < self.max_idle_time:
                        temp_connections.append(api)
                    else:
                        try:
                            api.close()
                            logger.debug("关闭空闲API连接")
                        except:
                            pass
                except queue.Empty:
                    break
            
            # 将未过期的连接放回池中
            for api in temp_connections:
                try:
                    self.connections.put_nowait(api)
                except queue.Full:
                    try:
                        api.close()
                    except:
                        pass
    
    def close_all(self):
        """关闭所有连接"""
        while not self.connections.empty():
            try:
                api = self.connections.get_nowait()
                api.close()
            except:
                pass
        logger.info(f"API连接池已关闭，总创建连接数: {self.total_connections_created}, 复用次数: {self.connection_reuse_count}")

class DatabaseConnectionPool:
    """数据库连接池管理器"""
    
    def __init__(self, max_connections: int = 10):
        self.max_connections = max_connections
        self.connections = {}  # db_path -> queue
        self.locks = {}  # db_path -> lock
        self.connection_counts = {}  # db_path -> count
        
    def _create_connection(self, db_path: str) -> sqlite3.Connection:
        """创建数据库连接"""
        conn = sqlite3.connect(db_path, timeout=30.0, check_same_thread=False)
        # 优化SQLite设置
        conn.execute("PRAGMA journal_mode=WAL")
        conn.execute("PRAGMA synchronous=NORMAL") 
        conn.execute("PRAGMA cache_size=10000")
        conn.execute("PRAGMA temp_store=MEMORY")
        conn.execute("PRAGMA mmap_size=268435456")  # 256MB
        return conn
    
    @contextmanager
    def get_connection(self, db_path: str):
        """获取数据库连接"""
        if db_path not in self.connections:
            self.connections[db_path] = queue.Queue(maxsize=self.max_connections)
            self.locks[db_path] = threading.Lock()
            self.connection_counts[db_path] = 0
        
        conn = None
        try:
            # 尝试从池中获取连接
            try:
                conn = self.connections[db_path].get_nowait()
            except queue.Empty:
                # 创建新连接
                conn = self._create_connection(db_path)
                self.connection_counts[db_path] += 1
                logger.debug(f"创建新数据库连接: {db_path}, 总数: {self.connection_counts[db_path]}")
            
            yield conn
            
        except Exception as e:
            logger.error(f"数据库连接使用过程中出错: {e}")
            if conn:
                try:
                    conn.rollback()
                except:
                    pass
            raise
        finally:
            # 将连接放回池中
            if conn:
                try:
                    conn.commit()
                    self.connections[db_path].put_nowait(conn)
                except queue.Full:
                    # 池已满，关闭连接
                    try:
                        conn.close()
                        self.connection_counts[db_path] -= 1
                    except:
                        pass
                except Exception as e:
                    logger.warning(f"放回数据库连接池时出错: {e}")
                    try:
                        conn.close()
                        self.connection_counts[db_path] -= 1
                    except:
                        pass
    
    def close_all(self):
        """关闭所有连接"""
        for db_path, conn_queue in self.connections.items():
            while not conn_queue.empty():
                try:
                    conn = conn_queue.get_nowait()
                    conn.close()
                except:
                    pass
        logger.info("数据库连接池已关闭")

class OptimizedDataCollector:
    """优化版数据采集器"""
    
    def __init__(self, H: int = 4, batch_size: int = 50, max_workers: int = 8):
        self.H = H
        self.batch_size = batch_size
        self.max_workers = max_workers
        self.running = True
        
        # 连接池
        self.api_pool = APIConnectionPool(pool_size=3)
        self.db_pool = DatabaseConnectionPool(max_connections=10)
        
        # 线程池
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        
        # 重试配置
        self.max_retries = 3
        self.base_retry_delay = 2
        
        # 性能指标
        self.metrics = PerformanceMetrics(start_time=datetime.now())
        
        # 内存监控
        self._log_memory_usage("初始化")
        
        logger.info(f"优化版数据采集器初始化完成 - 批次大小: {batch_size}, 工作线程: {max_workers}")
