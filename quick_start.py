#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速启动脚本 - 使用优化配置快速采集数据
"""

import time
import sys
import os
from data_collector import DataCollector
from config_optimized import get_optimized_config, print_config_summary
from logger_config import setup_logger

# 创建日志记录器
logger = setup_logger('quick_start')

def quick_collect():
    """快速数据采集"""
    print("🚀 启动快速数据采集...")
    
    # 显示优化配置
    print_config_summary()
    
    # 获取优化配置
    config = get_optimized_config()
    
    print(f"\n📊 使用配置: 历史数据{config['history_hours']}小时, 批处理{config['batch_size']}, 线程{config['max_workers']}")
    
    # 创建优化后的采集器
    collector = DataCollector(
        H=config['history_hours'],
        batch_size=config['batch_size'],
        max_workers=config['max_workers']
    )
    
    try:
        # 记录开始时间
        start_time = time.time()
        print(f"\n⏰ 开始时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 执行数据采集
        print("🔄 开始采集数据...")
        collector.批量处理(test=2)  # 只测试期货
        
        # 计算总耗时
        total_time = time.time() - start_time
        print(f"\n✅ 数据采集完成!")
        print(f"⏱️ 总耗时: {total_time:.2f} 秒 ({total_time/60:.1f} 分钟)")
        
        # 显示性能统计
        if hasattr(collector, 'performance_stats'):
            stats = collector.performance_stats
            if stats['contracts_processed'] > 0:
                print(f"📈 性能统计:")
                print(f"  处理合约数: {stats['contracts_processed']}")
                print(f"  平均每合约: {stats['avg_time_per_contract']:.2f} 秒")
                print(f"  并行效率: {(stats['contracts_processed'] * stats['avg_time_per_contract'] / stats['total_time']):.2f}x")
        
        # 性能对比
        original_time = 30 * 60  # 原来的30分钟
        speedup = original_time / total_time
        print(f"\n🚀 性能提升:")
        print(f"  原来需要: {original_time/60:.1f} 分钟")
        print(f"  现在需要: {total_time/60:.1f} 分钟")
        print(f"  速度提升: {speedup:.1f}x")
        
        if speedup >= 2:
            print("🎉 性能提升显著!")
        elif speedup >= 1.5:
            print("👍 性能有所提升")
        else:
            print("⚠️ 性能提升不明显，可能需要进一步优化")
            
    except Exception as e:
        print(f"❌ 数据采集失败: {e}")
        logger.error(f"数据采集失败: {e}")
    finally:
        collector.stop()
        print("🛑 采集器已停止")

def quick_test():
    """快速测试模式"""
    print("🧪 启动快速测试模式...")
    
    # 使用最小配置进行测试
    collector = DataCollector(
        H=1,           # 只采集1小时数据
        batch_size=50, # 小批次
        max_workers=8  # 较少线程
    )
    
    try:
        start_time = time.time()
        print("🔄 开始测试采集...")
        
        # 只测试少量合约
        collector.批量处理(test=2)
        
        test_time = time.time() - start_time
        print(f"✅ 测试完成，耗时: {test_time:.2f} 秒")
        
        # 估算完整采集时间
        if hasattr(collector, 'performance_stats'):
            stats = collector.performance_stats
            if stats['contracts_processed'] > 0:
                # 获取总合约数
                from contract_fetcher import get_active_contracts, get_active_options
                futures = get_active_contracts()[-2]
                options = get_active_options()
                total_contracts = len(futures) + len(options)
                
                estimated_time = (total_contracts / stats['contracts_processed']) * test_time
                print(f"📊 估算完整采集时间: {estimated_time/60:.1f} 分钟")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    finally:
        collector.stop()

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 交易数据采集 - 快速启动")
    print("=" * 60)
    
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
    else:
        print("\n请选择运行模式:")
        print("1. 快速采集 (推荐)")
        print("2. 快速测试")
        print("3. 查看配置")
        
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            mode = "collect"
        elif choice == "2":
            mode = "test"
        elif choice == "3":
            mode = "config"
        else:
            print("无效选择，使用默认模式")
            mode = "collect"
    
    try:
        if mode == "collect":
            quick_collect()
        elif mode == "test":
            quick_test()
        elif mode == "config":
            print_config_summary()
        else:
            print(f"未知模式: {mode}")
            quick_collect()
            
    except KeyboardInterrupt:
        print("\n\n⏹️ 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序运行出错: {e}")
        logger.error(f"程序运行出错: {e}")
    finally:
        print("\n🏁 程序结束")

if __name__ == "__main__":
    main() 