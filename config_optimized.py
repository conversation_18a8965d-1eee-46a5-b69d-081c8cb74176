# -*- coding: utf-8 -*-
"""
优化配置文件 - 高性能数据采集参数
"""

# 数据库配置
DB_PATH = 'market_data.db'

# TQSDK快期账户
TQ_USER = 'tqtq1866'
TQ_PASS = 'tqtq1866'

# === 性能优化配置 ===

# 历史数据配置
HISTORY_HOURS = 4          # 保持4小时历史数据，确保数据完整性
KLINE_PERIODS = ['1m']     # 只采集1分钟K线，减少数据量

# 批处理配置
BATCH_SIZE = 200           # 批处理大小 - 从50增加到200
MAX_WORKERS = 32           # 最大工作线程数 - 从8增加到32

# 网络优化配置
MAX_RETRIES = 2            # 最大重试次数 - 从3减少到2
RETRY_DELAY = 1            # 重试延迟（秒）- 从5减少到1
REQUEST_TIMEOUT = 120      # 请求超时时间（秒）- 从300减少到120

# 数据库优化配置
DB_TIMEOUT = 20.0          # 数据库连接超时
DB_CACHE_SIZE = 10000      # SQLite缓存大小
DB_SYNC_MODE = "NORMAL"    # 同步模式：NORMAL比FULL快

# 内存管理配置
MEMORY_CLEANUP_THRESHOLD = 512  # 内存清理阈值（MB）- 从1024减少到512
CLEANUP_INTERVAL = 30      # 清理间隔（秒）

# === 采集策略配置 ===

# 智能过滤配置
ENABLE_SMART_FILTER = True     # 启用智能过滤
MIN_DAILY_VOLUME = 50          # 最小日成交量阈值
ACTIVE_CONTRACTS_ONLY = True   # 只采集活跃合约

# 时间优化配置
TRADING_TIME_CHECK = True      # 启用交易时间检查
NIGHT_TRADING_ENABLED = True   # 启用夜盘交易

# === 监控配置 ===

# 性能监控
PERFORMANCE_MONITORING = True  # 启用性能监控
LOG_PERFORMANCE_STATS = True   # 记录性能统计
MEMORY_MONITORING = True       # 启用内存监控

# 告警配置
ALERT_MEMORY_THRESHOLD = 1024  # 内存告警阈值（MB）
ALERT_TIME_THRESHOLD = 600     # 时间告警阈值（秒）

# === 高级优化配置 ===

# 连接池配置
ENABLE_CONNECTION_POOL = True  # 启用连接池
MAX_CONNECTIONS = 50           # 最大连接数
CONNECTION_TIMEOUT = 30        # 连接超时

# 缓存配置
ENABLE_DATA_CACHE = True       # 启用数据缓存
CACHE_SIZE = 1000              # 缓存大小
CACHE_TTL = 300                # 缓存生存时间（秒）

# 异步处理配置
ENABLE_ASYNC_PROCESSING = False  # 暂时禁用异步处理（需要额外依赖）
ASYNC_BATCH_SIZE = 100         # 异步批处理大小

# === 配置验证函数 ===

def validate_config():
    """验证配置参数的有效性"""
    errors = []
    
    if HISTORY_HOURS < 1 or HISTORY_HOURS > 24:
        errors.append("HISTORY_HOURS 必须在1-24之间")
    
    if BATCH_SIZE < 10 or BATCH_SIZE > 500:
        errors.append("BATCH_SIZE 必须在10-500之间")
    
    if MAX_WORKERS < 1 or MAX_WORKERS > 64:
        errors.append("MAX_WORKERS 必须在1-64之间")
    
    if MAX_RETRIES < 1 or MAX_RETRIES > 5:
        errors.append("MAX_RETRIES 必须在1-5之间")
    
    if errors:
        raise ValueError(f"配置验证失败:\n" + "\n".join(f"  - {error}" for error in errors))
    
    return True

def get_optimized_config():
    """获取优化后的配置字典"""
    return {
        'history_hours': HISTORY_HOURS,
        'batch_size': BATCH_SIZE,
        'max_workers': MAX_WORKERS,
        'max_retries': MAX_RETRIES,
        'retry_delay': RETRY_DELAY,
        'request_timeout': REQUEST_TIMEOUT,
        'db_timeout': DB_TIMEOUT,
        'db_cache_size': DB_CACHE_SIZE,
        'db_sync_mode': DB_SYNC_MODE,
        'memory_cleanup_threshold': MEMORY_CLEANUP_THRESHOLD,
        'cleanup_interval': CLEANUP_INTERVAL,
        'enable_smart_filter': ENABLE_SMART_FILTER,
        'min_daily_volume': MIN_DAILY_VOLUME,
        'active_contracts_only': ACTIVE_CONTRACTS_ONLY,
        'trading_time_check': TRADING_TIME_CHECK,
        'night_trading_enabled': NIGHT_TRADING_ENABLED,
        'performance_monitoring': PERFORMANCE_MONITORING,
        'log_performance_stats': LOG_PERFORMANCE_STATS,
        'memory_monitoring': MEMORY_MONITORING,
        'alert_memory_threshold': ALERT_MEMORY_THRESHOLD,
        'alert_time_threshold': ALERT_TIME_THRESHOLD,
        'enable_connection_pool': ENABLE_CONNECTION_POOL,
        'max_connections': MAX_CONNECTIONS,
        'connection_timeout': CONNECTION_TIMEOUT,
        'enable_data_cache': ENABLE_DATA_CACHE,
        'cache_size': CACHE_SIZE,
        'cache_ttl': CACHE_TTL,
        'enable_async_processing': ENABLE_ASYNC_PROCESSING,
        'async_batch_size': ASYNC_BATCH_SIZE
    }

def print_config_summary():
    """打印配置摘要"""
    print("=" * 60)
    print("🚀 优化配置摘要")
    print("=" * 60)
    
    config = get_optimized_config()
    
    print(f"📊 性能参数:")
    print(f"  历史数据: {config['history_hours']} 小时 (保持4小时确保数据完整性)")
    print(f"  批处理大小: {config['batch_size']}")
    print(f"  工作线程: {config['max_workers']}")
    print(f"  重试次数: {config['max_retries']}")
    print(f"  重试延迟: {config['retry_delay']} 秒")
    
    print(f"\n🗄️ 数据库配置:")
    print(f"  连接超时: {config['db_timeout']} 秒")
    print(f"  缓存大小: {config['db_cache_size']}")
    print(f"  同步模式: {config['db_sync_mode']}")
    
    print(f"\n🧠 内存管理:")
    print(f"  清理阈值: {config['memory_cleanup_threshold']} MB")
    print(f"  清理间隔: {config['cleanup_interval']} 秒")
    
    print(f"\n⚡ 优化特性:")
    print(f"  智能过滤: {'启用' if config['enable_smart_filter'] else '禁用'}")
    print(f"  连接池: {'启用' if config['enable_connection_pool'] else '禁用'}")
    print(f"  数据缓存: {'启用' if config['enable_data_cache'] else '禁用'}")
    print(f"  性能监控: {'启用' if config['performance_monitoring'] else '禁用'}")
    
    print(f"\n⚠️ 重要说明:")
    print(f"  - 保持4小时历史数据以确保数据完整性")
    print(f"  - 通过并行处理和数据库优化提升性能")
    print(f"  - 不牺牲数据质量换取速度")
    
    print("=" * 60)

if __name__ == "__main__":
    try:
        validate_config()
        print_config_summary()
        print("✅ 配置验证通过")
    except Exception as e:
        print(f"❌ 配置验证失败: {e}") 