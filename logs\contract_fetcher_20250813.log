2025-08-13 23:18:44 - contract_fetcher - INFO - 主合约: ['CZCE.CY509', 'CFFEX.TS2512', 'CFFEX.TS2509', 'CZCE.TA601', 'CZCE.TA509', 'CZCE.CF601', 'CZCE.CF509', 'DCE.y2509', 'DCE.y2601', 'SHFE.au2510', 'SHFE.au2508', 'CFFEX.IC2509', 'CZCE.FG509', 'CZCE.FG601', 'SHFE.cu2508', 'SHFE.al2508', 'DCE.cs2509', 'SHFE.ru2601', 'SHFE.ru2509', 'DCE.b2509', 'DCE.b2508', 'CZCE.RM509', 'CZCE.RM601', 'SHFE.hc2510', 'SHFE.hc2511', 'INE.bc2508', 'DCE.v2601', 'DCE.v2509', 'SHFE.rb2510', 'SHFE.rb2509', 'SHFE.ag2510', 'SHFE.ag2508', 'SHFE.pb2508', 'DCE.l2601', 'DCE.l2509', 'SHFE.sp2509', 'CFFEX.IH2509', 'SHFE.fu2509', 'SHFE.zn2508', 'SHFE.ni2508', 'DCE.jm2509', 'DCE.jm2601', 'DCE.eg2509', 'DCE.eg2601', 'INE.nr2508', 'DCE.a2509', 'DCE.m2509', 'DCE.m2601', 'SHFE.bu2509', 'GFEX.lc2509', 'SHFE.sn2508', 'CFFEX.IF2509', 'DCE.i2601', 'DCE.i2509', 'CZCE.SA509', 'CZCE.SA601', 'DCE.pg2508', 'INE.lu2509', 'CFFEX.IM2509', 'CZCE.PF508', 'CZCE.MA509', 'CZCE.MA601', 'SHFE.ss2508', 'CFFEX.T2509', 'CFFEX.T2512', 'CZCE.SR509', 'CZCE.SR511', 'CFFEX.TF2509', 'CFFEX.TF2512', 'DCE.j2601', 'DCE.j2509', 'DCE.c2509', 'SHFE.ao2510', 'SHFE.ao2509', 'DCE.p2601', 'DCE.p2509', 'GFEX.si2509', 'DCE.eb2508', 'SHFE.ad2511', 'SHFE.ad2512', 'CZCE.SH509', 'CZCE.SH508', 'CZCE.PR509', 'CZCE.PR508', 'CFFEX.TL2512', 'CFFEX.TL2509', 'CZCE.OI509', 'CZCE.OI601', 'SHFE.br2508', 'DCE.pp2509', 'DCE.pp2601', 'CZCE.PX511', 'CZCE.PX509', 'GFEX.ps2508', 'CZCE.UR509', 'CZCE.UR601']
2025-08-13 23:18:44 - contract_fetcher - INFO - 到期时间在45天内的合约: ['DCE.jd2509', 'DCE.y2509', 'SHFE.cu2509', 'SHFE.al2509', 'DCE.cs2509', 'SHFE.ru2509', 'DCE.b2509', 'DCE.v2509', 'SHFE.rb2509', 'DCE.lh2509', 'SHFE.ag2509', 'DCE.l2509', 'SHFE.zn2509', 'SHFE.ni2509', 'DCE.eg2509', 'DCE.a2509', 'DCE.m2509', 'DCE.i2509', 'DCE.pg2509', 'DCE.c2509', 'SHFE.ao2509', 'DCE.p2509', 'DCE.eb2509', 'SHFE.br2509', 'DCE.pp2509', 'DCE.lg2509']
2025-08-13 23:18:44 - contract_fetcher - INFO - 主合约,并且期权有效期在80天内的: ['CZCE.CY507', 'CZCE.CY509', 'CFFEX.TS2512', 'CFFEX.TS2509', 'CZCE.TA509', 'CZCE.CF509', 'DCE.y2509', 'SHFE.au2510', 'SHFE.au2508', 'CFFEX.IC2509', 'CFFEX.IC2506', 'CZCE.FG509', 'SHFE.cu2508', 'SHFE.cu2507', 'SHFE.al2507', 'SHFE.al2508', 'DCE.cs2507', 'DCE.cs2509', 'SHFE.ru2509', 'DCE.b2509', 'DCE.b2508', 'CZCE.RM509', 'SHFE.hc2510', 'SHFE.hc2511', 'INE.bc2507', 'INE.bc2508', 'DCE.v2509', 'SHFE.rb2510', 'SHFE.rb2509', 'SHFE.ag2510', 'SHFE.ag2508', 'SHFE.pb2508', 'SHFE.pb2507', 'DCE.l2509', 'SHFE.sp2509', 'SHFE.sp2507', 'CFFEX.IH2509', 'CFFEX.IH2506', 'SHFE.fu2507', 'SHFE.fu2509', 'SHFE.zn2507', 'SHFE.zn2508', 'SHFE.ni2507', 'SHFE.ni2508', 'DCE.jm2509', 'DCE.jm2601', 'DCE.eg2509', 'INE.nr2507', 'INE.nr2508', 'DCE.a2507', 'DCE.a2509', 'DCE.m2509', 'SHFE.bu2509', 'SHFE.bu2507', 'GFEX.lc2509', 'GFEX.lc2507', 'CZCE.UR506', 'SHFE.sn2508', 'SHFE.sn2507', 'CFFEX.IF2509', 'CFFEX.IF2506', 'DCE.i2509', 'CZCE.SA509', 'DCE.pg2507', 'DCE.pg2508', 'INE.lu2508', 'INE.lu2509', 'CFFEX.IM2506', 'CFFEX.IM2509', 'CZCE.PF507', 'CZCE.PF508', 'CZCE.MA509', 'SHFE.ss2507', 'SHFE.ss2508', 'CFFEX.T2509', 'CFFEX.T2512', 'CZCE.SR509', 'CZCE.SR511', 'CFFEX.TF2509', 'CFFEX.TF2512', 'DCE.j2601', 'DCE.j2509', 'DCE.c2509', 'DCE.c2507', 'SHFE.ao2510', 'SHFE.ao2509', 'DCE.p2509', 'GFEX.si2509', 'GFEX.si2507', 'DCE.eb2507', 'DCE.eb2508', 'INE.sc2508', 'INE.sc2507', 'SHFE.ad2511', 'CZCE.SH509', 'CZCE.SH508', 'CZCE.PR509', 'CZCE.PR508', 'CFFEX.TL2512', 'CFFEX.TL2509', 'CZCE.OI509', 'SHFE.br2508', 'SHFE.br2507', 'DCE.pp2509', 'CZCE.PX511', 'CZCE.PX509', 'GFEX.ps2507', 'GFEX.ps2508', 'CZCE.UR509']
2025-08-13 23:18:44 - contract_fetcher - INFO - 合并的一个合约: ['DCE.b2508', 'SHFE.sp2509', 'DCE.eg2509', 'DCE.pg2507', 'CZCE.TA601', 'SHFE.ni2508', 'INE.nr2507', 'DCE.pg2509', 'DCE.p2509', 'CZCE.CF509', 'SHFE.pb2507', 'SHFE.ss2507', 'CZCE.CY509', 'CFFEX.IC2509', 'SHFE.al2507', 'SHFE.ag2510', 'DCE.eb2508', 'CZCE.PF507', 'CZCE.CY507', 'SHFE.zn2509', 'SHFE.cu2508', 'SHFE.ni2509', 'DCE.y2601', 'INE.sc2507', 'DCE.l2601', 'DCE.cs2509', 'SHFE.rb2510', 'CZCE.UR601', 'INE.lu2508', 'DCE.lh2509', 'DCE.lg2509', 'GFEX.si2509', 'SHFE.ru2509', 'CFFEX.IF2509', 'DCE.pp2601', 'DCE.p2601', 'CZCE.SR509', 'GFEX.lc2509', 'CFFEX.IF2506', 'CFFEX.TL2512', 'CZCE.RM601', 'DCE.eg2601', 'DCE.a2507', 'SHFE.hc2510', 'CZCE.SR511', 'CZCE.MA601', 'CFFEX.IM2506', 'SHFE.fu2509', 'CZCE.UR506', 'CFFEX.TF2509', 'DCE.pg2508', 'CZCE.RM509', 'CZCE.PR509', 'INE.nr2508', 'DCE.v2601', 'SHFE.br2508', 'DCE.eb2509', 'CZCE.PR508', 'GFEX.lc2507', 'SHFE.ao2510', 'CFFEX.T2512', 'CFFEX.IM2509', 'INE.lu2509', 'SHFE.ao2509', 'DCE.cs2507', 'DCE.a2509', 'DCE.l2509', 'SHFE.au2510', 'SHFE.sp2507', 'DCE.i2601', 'SHFE.zn2507', 'DCE.j2509', 'CZCE.PX511', 'CZCE.SH509', 'SHFE.ni2507', 'SHFE.ad2512', 'CFFEX.IH2509', 'DCE.v2509', 'DCE.m2509', 'INE.bc2507', 'CZCE.UR509', 'CFFEX.TS2512', 'SHFE.fu2507', 'DCE.eb2507', 'SHFE.pb2508', 'CZCE.OI509', 'CZCE.SH508', 'DCE.jm2509', 'DCE.j2601', 'SHFE.al2508', 'SHFE.zn2508', 'SHFE.sn2507', 'GFEX.si2507', 'GFEX.ps2507', 'CZCE.SA601', 'DCE.m2601', 'SHFE.ag2509', 'SHFE.ad2511', 'CZCE.FG601', 'SHFE.sn2508', 'SHFE.cu2509', 'SHFE.ss2508', 'CZCE.FG509', 'DCE.b2509', 'CZCE.PF508', 'CZCE.TA509', 'GFEX.ps2508', 'CFFEX.TS2509', 'SHFE.bu2507', 'CFFEX.TF2512', 'INE.sc2508', 'SHFE.ru2601', 'DCE.pp2509', 'SHFE.rb2509', 'CFFEX.IC2506', 'DCE.c2507', 'SHFE.al2509', 'CZCE.OI601', 'DCE.y2509', 'SHFE.br2509', 'SHFE.cu2507', 'DCE.c2509', 'CFFEX.T2509', 'SHFE.ag2508', 'CFFEX.IH2506', 'DCE.jm2601', 'INE.bc2508', 'CZCE.SA509', 'SHFE.au2508', 'SHFE.bu2509', 'CZCE.CF601', 'DCE.jd2509', 'SHFE.hc2511', 'CZCE.PX509', 'CZCE.MA509', 'SHFE.br2507', 'DCE.i2509', 'CFFEX.TL2509']
2025-08-13 23:18:44 - contract_fetcher - INFO - 合并的一个合约,并且期权有效期在80天内的: ['DCE.b2508', 'SHFE.sp2509', 'DCE.eg2509', 'DCE.pg2507', 'SHFE.ni2508', 'INE.nr2507', 'DCE.pg2509', 'DCE.p2509', 'CZCE.CF509', 'SHFE.pb2507', 'SHFE.ss2507', 'CZCE.CY509', 'CFFEX.IC2509', 'SHFE.al2507', 'SHFE.ag2510', 'DCE.eb2508', 'CZCE.PF507', 'CZCE.CY507', 'SHFE.zn2509', 'SHFE.cu2508', 'INE.sc2507', 'SHFE.ni2509', 'DCE.cs2509', 'SHFE.rb2510', 'INE.lu2508', 'DCE.lh2509', 'DCE.lg2509', 'GFEX.si2509', 'SHFE.ru2509', 'CFFEX.IF2509', 'CZCE.SR509', 'GFEX.lc2509', 'CFFEX.IF2506', 'CFFEX.TL2512', 'DCE.a2507', 'SHFE.hc2510', 'CZCE.SR511', 'CFFEX.IM2506', 'SHFE.fu2509', 'CZCE.UR506', 'CFFEX.TF2509', 'DCE.pg2508', 'CZCE.RM509', 'CZCE.PR509', 'INE.nr2508', 'DCE.eb2509', 'SHFE.br2508', 'CZCE.PR508', 'GFEX.lc2507', 'SHFE.ao2510', 'CFFEX.T2512', 'CFFEX.IM2509', 'INE.lu2509', 'SHFE.ao2509', 'DCE.cs2507', 'DCE.a2509', 'DCE.l2509', 'SHFE.au2510', 'SHFE.sp2507', 'SHFE.zn2507', 'DCE.j2509', 'CZCE.PX511', 'CZCE.SH509', 'SHFE.ni2507', 'CFFEX.IH2509', 'DCE.v2509', 'DCE.m2509', 'INE.bc2507', 'CZCE.UR509', 'CFFEX.TS2512', 'SHFE.fu2507', 'DCE.eb2507', 'SHFE.pb2508', 'CZCE.OI509', 'CZCE.SH508', 'DCE.jm2509', 'DCE.j2601', 'SHFE.al2508', 'SHFE.zn2508', 'SHFE.sn2507', 'GFEX.si2507', 'GFEX.ps2507', 'SHFE.ag2509', 'SHFE.ad2511', 'SHFE.sn2508', 'SHFE.cu2509', 'SHFE.ss2508', 'CZCE.FG509', 'DCE.b2509', 'CZCE.PF508', 'CZCE.TA509', 'GFEX.ps2508', 'CFFEX.TS2509', 'SHFE.bu2507', 'CFFEX.TF2512', 'INE.sc2508', 'DCE.pp2509', 'CFFEX.IC2506', 'SHFE.rb2509', 'DCE.c2507', 'SHFE.al2509', 'DCE.y2509', 'SHFE.br2509', 'SHFE.cu2507', 'DCE.c2509', 'CFFEX.T2509', 'SHFE.ag2508', 'CFFEX.IH2506', 'DCE.jm2601', 'INE.bc2508', 'CZCE.SA509', 'SHFE.au2508', 'SHFE.bu2509', 'DCE.jd2509', 'SHFE.hc2511', 'CZCE.PX509', 'CZCE.MA509', 'SHFE.br2507', 'DCE.i2509', 'CFFEX.TL2509']
2025-08-13 23:18:44 - contract_fetcher - INFO - 当前小时时间: 23
2025-08-13 23:18:44 - contract_fetcher - INFO - 过滤前品种列表: 138
2025-08-13 23:18:44 - contract_fetcher - INFO - 过滤后品种列表: 112
2025-08-13 23:18:44 - contract_fetcher - INFO - 当前小时时间: 23
2025-08-13 23:18:44 - contract_fetcher - INFO - 过滤前品种列表: 120
2025-08-13 23:18:44 - contract_fetcher - INFO - 过滤后品种列表: 96
2025-08-13 23:18:44 - contract_fetcher - INFO - 获取到 366 个未过期的期权合约----用于采集数据
2025-08-13 23:18:44 - contract_fetcher - INFO - 当前小时时间: 23
2025-08-13 23:18:44 - contract_fetcher - INFO - 过滤前品种列表: 366
2025-08-13 23:18:44 - contract_fetcher - INFO - 过滤后品种列表: 204
2025-08-13 23:18:44 - contract_fetcher - INFO - 时间过滤后剩余 204 个期权合约----用于采集数据
