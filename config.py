# 配置文件
DB_PATH = 'market_data.db'  # SQLite数据库文件名

# TQSDK快期账户（请填写你的快期用户名和密码）
TQ_USER = 'tqtq1866'
TQ_PASS = 'tqtq1866'

# --- 数据采集配置 ---
# 采集的K线周期（1m, 5m, 1h, 1d）
KLINE_PERIODS = ['1m', '5m']
# 是否采集Tick数据
COLLECT_TICK = True
# 是否采集盘口数据
COLLECT_DEPTH = True

# --- 性能与过滤配置 ---
# 分批采集的批次大小（根据网络和性能调整）
BATCH_SIZE = 50
# 筛选活跃合约的最小日成交量
MIN_DAILY_VOLUME = 100
# 调度器执行采集任务的间隔（分钟）
JOB_INTERVAL_MINUTES = 5

FETCH_INTERVAL = 1           # 盘口采集间隔（秒） 

def get_code(option_symbol: str) -> str:
    """从期权合约代码中提取期货合约代码"""
    代码=option_symbol.split('.')[-1]
    if len(代码)>8:
        品种类别="期权"
    else:
        品种类别="期货"

    if 品种类别=="期权":
        # 去除所有-
        parts = 代码.replace('-', '')
        
        # 找到最后一个C或P的位置，截取之前的部分
        c_pos = parts.rfind('C')
        p_pos = parts.rfind('P')
        if c_pos != -1 or p_pos != -1:
            pos = max(c_pos, p_pos)
            期货合约代码 = parts[:pos]
        else:
            期货合约代码 =parts
    else:
        期货合约代码 =代码
    
    #除去数字
    品种代码=代码[:2].rstrip('0123456789')
    # print(品种代码,期货合约代码)
    return 品种代码,期货合约代码

