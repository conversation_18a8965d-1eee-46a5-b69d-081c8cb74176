import sqlite3
import os
from datetime import datetime
from typing import List, Dict, Any
from config import get_code

# 数据库路径配置
DB_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
if not os.path.exists(DB_DIR):
    os.makedirs(DB_DIR)

# 合约信息数据库
CONTRACTS_DB = os.path.join(DB_DIR, 'contracts.db')

# # 行情数据库（按品种分类）
def get_market_db_path(product_id: str) -> str:
    """获取指定品种的行情数据库路径"""
    #合约代码处理
    品种代码,期货合约代码=get_code(product_id)
    os.makedirs(os.path.join(DB_DIR, 品种代码), exist_ok=True)
    return os.path.join(DB_DIR, 品种代码,f'QH_{期货合约代码}.db')

def init_contracts_db():
    """初始化合约信息数据库"""
    conn = sqlite3.connect(CONTRACTS_DB)
    c = conn.cursor()
    
    # 创建合约信息表
    c.execute('''
        CREATE TABLE IF NOT EXISTS contracts (
            symbol TEXT PRIMARY KEY,
            exchange TEXT,
            name TEXT,
            type TEXT,
            underlying TEXT,
            listed_date TEXT,
            expired_date REAL,
            volume INTEGER,
            is_main INTEGER
        )
    ''')
    
    conn.commit()
    conn.close()

def init_market_db(product_id: str):
    """初始化指定品种的行情数据库"""
    db_path = get_market_db_path(product_id)
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    
    # 创建K线数据表
    c.execute('''
        CREATE TABLE IF NOT EXISTS klines (
            id INTEGER,
            symbol TEXT,
            datetime INTEGER,
            open REAL,
            high REAL,
            low REAL,
            close REAL,
            volume INTEGER,
            open_oi INTEGER,
            close_oi INTEGER,
            PRIMARY KEY (id)
        )
    ''')
    
    # 创建Tick数据表
    c.execute('''
        CREATE TABLE IF NOT EXISTS ticks (
            symbol TEXT,
            datetime TEXT,
            last_price REAL,
            volume INTEGER,
            amount REAL,
            open_interest INTEGER,
            bid_price1 REAL,
            bid_volume1 INTEGER,
            ask_price1 REAL,
            ask_volume1 INTEGER,
            PRIMARY KEY (symbol, datetime)
        )
    ''')
    
    # 创建索引
    c.execute('CREATE INDEX IF NOT EXISTS idx_klines_symbol ON klines(symbol)')
    c.execute('CREATE INDEX IF NOT EXISTS idx_ticks_symbol ON ticks(symbol)')
    
    conn.commit()
    conn.close()

def insert_many(table: str, data: List[Dict[str, Any]], product_id: str = None):
    """批量插入数据到指定表"""
    if product_id:
        db_path = get_market_db_path(product_id)
    else:
        db_path = CONTRACTS_DB
        
    conn = sqlite3.connect(db_path)
    c = conn.cursor()
    
    if data:
        columns = list(data[0].keys())
        placeholders = ','.join(['?' for _ in columns])
        query = f'INSERT OR REPLACE INTO {table} ({",".join(columns)}) VALUES ({placeholders})'
        
        values = [[row[col] for col in columns] for row in data]
        c.executemany(query, values)
        
    conn.commit()
    conn.close()

def get_product_id(symbol: str) -> str:
    """从合约代码中提取品种代码"""
    return symbol.split('.')[1].rstrip('0123456789')

def init_all_dbs():
    """初始化所有数据库"""
    init_contracts_db()
    # 获取所有品种并初始化对应的行情数据库
    conn = sqlite3.connect(CONTRACTS_DB)
    c = conn.cursor()
    c.execute("SELECT DISTINCT substr(symbol, instr(symbol, '.') + 1, 2) FROM contracts")
    # products = [row[0] for row in c.fetchall()]
    conn.close()
    
    # for product in products:
    #     init_market_db(product) 