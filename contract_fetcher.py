from tqsdk import Tq<PERSON><PERSON>, TqAuth, TqKq
from db import insert_many, CONTRACTS_DB
from config import TQ_USER, TQ_PASS, MIN_DAILY_VOLUME
import pandas as pd
import sqlite3
from datetime import date, timedelta
from typing import List
import time
import re
from datetime import datetime
import logging
from logger_config import setup_logger

# 创建日志记录器
logger = setup_logger('contract_fetcher')

#按时间过滤需要采集的品种
def filter_by_time(symbols,test=1):
    #凌晨场
    pc2=["au","ag","sc"]
    pc1=["cu","al","zn","ni","ad","sn","pb","bc"]
    #夜盘
    # pc23=["br" bu fu hc lu nr rb ru sp ss a b c cs eb]
    #日盘
    pc15=["IF","IH","IC","IM","T","TF","TS","lc","si","UR","PK","jd","lh","lg","MO","IO","HO","ec","SF","RM"]
    #当前小时时间
    pctest=["UR","SF","SM","PK","ec","CJ","AP"]
    now=datetime.now()
    hour=now.hour
    logger.info(f"当前小时时间: {hour}")
    logger.info(f"过滤前品种列表: {len(symbols)}")
    #过滤品种
    if test!=1:
        new_symbols=[]
        for i in symbols:
            pz=i.split('.')[1][:2]
            pz=re.sub(r'\d+', '', pz)
            logger.debug(f"品种代码: {pz}")
            if pz in pctest:
                new_symbols.append(i)
        logger.info(f"test过滤后品种列表: {len(new_symbols)}, {new_symbols}")
        return new_symbols

    new_symbols=[]
    for i in symbols:
        pz=i.split('.')[1][:2]
        pz=re.sub(r'\d+', '', pz)
        if 20<hour<=23:
            #排除白盘品种
            if pz not in pc15:
                new_symbols.append(i)
        elif hour <=2:
            #只留凌晨场品种
            if pz in pc2+pc1:
                new_symbols.append(i)
        elif hour ==3:
            #只留凌晨场3点品种
            if pz in pc2:
                new_symbols.append(i)
        else:
            #其他时间保留所有品种
            new_symbols.append(i)

    logger.info(f"过滤后品种列表: {len(new_symbols)}")
    return new_symbols



def fetch_and_save_contracts():
    """获取所有期货合约信息并存入数据库，标记主力合约"""
    logger.info("开始获取所有期货合约信息...")
    try:
        logger.info("正在连接天勤服务器...")
        api = TqApi(TqKq(), auth=TqAuth(TQ_USER, TQ_PASS))
        logger.info("连接成功！")
        
        # 获取所有期货合约代码
        logger.info("正在获取期货合约列表...")
        symbols = api.query_quotes(ins_class="FUTURE")
        if not symbols:
            logger.warning("未能获取到任何期货合约信息。")
            return
        logger.info(f"获取到 {len(symbols)} 个期货合约")


        #获取当前有那些品种合约
        pzli = []
        #排除不做的死品
        lixx=["rr","fb","bb","wr","RS","JR","LR","PM","RI","WH","ZC"]
        for i in symbols:
            pz=i.split('.')[1]
            #去除数字
            pz=re.sub(r'\d+', '', pz)
            if pz not in pzli and "@" not in pz and pz not in lixx:
                pzli.append(pz)
        logger.info(f"找到的品种列表: {pzli}")
        # pzli=["UR","SF","SM","PK","ec","CJ","AP"]
        # pzli=["UR"]
       

        contract_list = []
        for pz in pzli:  # 处理所有品种
            logger.info(f"开始处理品种 {pz}...")
            api2=TqApi(TqKq(), auth=TqAuth(TQ_USER, TQ_PASS))
            batch_symbols = api2.query_quotes(ins_class="FUTURE", product_id=pz,expired=False)
            logger.info(f"正在处理 {pz} 合约，共 {len(batch_symbols)} 个...")

            # 批量获取当前批次的合约信息
            logger.info("正在获取合约详细信息...")
            quotes = api2.get_quote_list(batch_symbols)
            # print(tick)
            # api.wait_update()
            logger.info("获取合约信息完成")
            
            # 获取主力合约
            mian_volume_1 = 0
            mian_volume_2 = 0
            mian_symbol_1 = None
            mian_symbol_2 = None

            # 找出成交量最大的两个合约
            volumes = []
            for symbol, quote in zip(batch_symbols, quotes):
                tick = api2.get_tick_serial(symbol, 60)
                volume = tick.volume.iloc[-2]
                volumes.append((symbol, volume))

            # 按成交量降序排序
            volumes.sort(key=lambda x: x[1], reverse=True)

            # 取前两个
            if len(volumes) >= 2:
                mian_symbol_1, mian_volume_1 = volumes[0]
                mian_symbol_2, mian_volume_2 = volumes[1]
            elif len(volumes) == 1:
                mian_symbol_1, mian_volume_1 = volumes[0]
                mian_symbol_2, mian_volume_2 = None, 0
            else:
                mian_symbol_1, mian_volume_1 = None, 0
                mian_symbol_2, mian_volume_2 = None, 0

            logger.info(f"主力合约1: {mian_symbol_1}, 成交量: {mian_volume_1}")
            logger.info(f"主力合约2: {mian_symbol_2}, 成交量: {mian_volume_2}")

            # 保存合约信息
            for symbol, quote in zip(batch_symbols, quotes):
                is_main = symbol in [mian_symbol_1, mian_symbol_2]
                #获取期权信息
                ls = api2.query_options(symbol, option_class="PUT", expired=False)
                if ls:
                    qq=ls[0]
                    logger.info(f"期权信息: {qq}")
                    #获取期权信息
                    qq_quote=api2.get_quote(qq)
                    # print(f"期权信息: {qq_quote}")
                    #期权到期时间
                    expire_timestamp = getattr(qq_quote, 'last_exercise_datetime', 0)
                    if expire_timestamp:
                        expire_date = datetime.fromtimestamp(expire_timestamp)
                        logger.info(f"期权 {qq} 到期日期: {expire_date}")
                    else:
                        logger.warning(f"期权 {qq} 没有 last_exercise_datetime 字段")
                else:
                    logger.warning(f"期权 {symbol} 没有期权信息")
                    expire_timestamp = 0

                try:
                    contract_list.append({
                        'symbol': symbol,
                        'exchange': getattr(quote, 'exchange_id', ''),
                        'name': getattr(quote, 'instrument_name', ''),
                        'type': 'FUTURE',
                        'underlying': getattr(quote, 'underlying_symbol', ''),
                        'listed_date': int(expire_timestamp),
                        'expired_date': getattr(quote, 'expire_datetime', ''),
                        'volume': getattr(quote, 'volume', 0),
                        'is_main': is_main
                    })
                    logger.info(f"已保存合约 {symbol} 的信息")
                except Exception as e:
                    logger.error(f"保存合约 {symbol} 信息时出错: {e}")
                    continue
            api2.close()
        if contract_list:
            logger.info("正在保存合约信息到数据库...")
            insert_many('contracts', contract_list)
            logger.info(f"合约信息更新成功，共获取 {len(contract_list)} 个期货合约。")
        else:
            logger.warning("未能获取到任何期货合约信息。")
    except Exception as e:
        logger.error(f"获取合约信息失败: {e}")
        api.close()
    finally:
        if 'api' in locals() and api:
            api.close()

def get_active_contracts(test=1):
    """获取所有未过期的主合约，以及到期时间在45天内的合约"""
    conn = sqlite3.connect(CONTRACTS_DB)
    c = conn.cursor()
    now = datetime.now()
    # 获取所有未过期的主合约
    c.execute("SELECT symbol FROM contracts WHERE is_main = 1 AND expired_date > ?", (now.timestamp(),))
    main_contracts = [row[0] for row in c.fetchall()]
    
    # 获取到期时间在45天内的合约，并且期权在有效期内的
    c.execute("""
        SELECT symbol FROM contracts 
        WHERE expired_date > ? 
        AND expired_date < ? 
        AND listed_date > ?  -- 确保期权在有效期内
    """, (now.timestamp(), (now + timedelta(days=45)).timestamp(), now.timestamp()))
    near_expiry_contracts = [row[0] for row in c.fetchall()]

    #获取主合约，并且期权有效期在80天内的
    c.execute("""
        SELECT symbol FROM contracts 
        WHERE is_main = 1 
        AND listed_date < ?  -- 确保期权在有效期内
    """, ((now + timedelta(days=80)).timestamp(),))
    main_contracts_80 = [row[0] for row in c.fetchall()]
    
    conn.close()
    #合并的一个合约,去除重复的  
    active_list = list(set(main_contracts + near_expiry_contracts+main_contracts_80))
    qq_list = list(set(main_contracts_80 + near_expiry_contracts))
    logger.info(f"主合约: {main_contracts}")
    logger.info(f"到期时间在45天内的合约: {near_expiry_contracts}")
    logger.info(f"主合约,并且期权有效期在80天内的: {main_contracts_80}")
    logger.info(f"合并的一个合约: {active_list}")
    logger.info(f"合并的一个合约,并且期权有效期在80天内的: {qq_list}")
    #过滤品种
    active_list=filter_by_time(active_list,test)
    qq_list=filter_by_time(qq_list,test)
    return main_contracts, near_expiry_contracts, active_list,qq_list

#获取中金所的期权合约
def get_cffex_options():
    try:
        api = TqApi(TqKq(), auth=TqAuth(TQ_USER, TQ_PASS))
        q=api.query_quotes(ins_class="OPTION",expired=False)
        zjs=[]
        for i in q:
            if "CFFEX" in i:    
                zjs.append(i)
        #对期权进行分组
        zjs_group={}
        for i in zjs:
            zpj=i[6:12]
            if "-C-" in i:
                qq_type="CALL"
            else:
                qq_type="PUT"
            zpj=zpj+"-"+qq_type
            if zpj not in zjs_group:
                zjs_group[zpj]=[]
            zjs_group[zpj].append(i)
        logger.info(f"中金所期权分组: {zjs_group}")
        return zjs_group

    except Exception as e:
        logger.error(f"获取中金所期权合约失败: {e}")
        return {}

def qq_list处理(qq_list,qq_type,future_symbol):
    """处理期权列表"""
    try:
        api = TqApi(TqKq(), auth=TqAuth(TQ_USER, TQ_PASS))
        qq_quotes = api.get_quote_list(qq_list)
        qq_data = []
        for qq, quote in zip(qq_list, qq_quotes):
            try:
                qq_data.append({
                    'symbol': qq,
                    'exchange': getattr(quote, 'exchange_id', ''),
                    'name': getattr(quote, 'instrument_name', ''),
                    'type': 'OPTION',
                    'underlying': future_symbol,
                    'option_type': qq_type,
                    'listed_date': getattr(quote, 'listed_date', 0),
                    'expired_date': getattr(quote, 'expire_datetime', 0),
                    'volume': getattr(quote, 'volume', 0),
                    'is_main': False
                })
                logger.info(f"已保存期权 {qq} 的信息")
            except Exception as e:
                logger.error(f"保存期权 {qq} 信息时出错: {e}")
                continue
        return qq_data
    except Exception as e:
        logger.error(f"处理期权列表失败: {e}")
        return []

def fetch_and_save_options():
    """获取所有期权合约信息并存入数据库"""
    logger.info("开始获取所有期权合约信息...")
    try:
        logger.info("正在连接天勤服务器...")
        api = TqApi(TqKq(), auth=TqAuth(TQ_USER, TQ_PASS))
        logger.info("连接成功！")
        
        # 获取所有期权合约代码
        logger.info("正在获取期权合约列表...")
        symbols = api.query_quotes(ins_class="OPTION")
        if not symbols:
            logger.warning("未能获取到任何期权合约信息。")
            return
        logger.info(f"获取到 {len(symbols)} 个期权合约")

        # 获取中金所期权合约
        zjs_group = get_cffex_options()
        if not zjs_group:
            logger.warning("未能获取到中金所期权合约信息。")
            return

        # 处理每个期权组
        for zpj, qq_list in zjs_group.items():
            logger.info(f"正在处理期权组 {zpj}...")
            qq_type = "CALL" if "-C-" in qq_list[0] else "PUT"
            future_symbol = qq_list[0].split('.')[1][:6]
            
            # 处理期权列表
            qq_data = qq_list处理(qq_list, qq_type, future_symbol)
            if qq_data:
                logger.info(f"正在保存期权组 {zpj} 的信息到数据库...")
                insert_many('contracts', qq_data)
                logger.info(f"期权组 {zpj} 信息更新成功，共获取 {len(qq_data)} 个期权合约。")
            else:
                logger.warning(f"未能获取到期权组 {zpj} 的任何信息。")

    except Exception as e:
        logger.error(f"获取期权合约信息失败: {e}")
    finally:
        if 'api' in locals() and api:
            api.close()


def get_active_options(test=1):
    """获取所有未过期的期权合约"""
    conn = sqlite3.connect(CONTRACTS_DB)
    c = conn.cursor()
    now = datetime.now()
    
    # 获取所有未过期的期权合约
    c.execute("""
        SELECT symbol FROM options 
        WHERE expire_date > ?
    """, (now.timestamp()-3600,))
    options = [row[0] for row in c.fetchall()]
    
    conn.close()
    logger.info(f"获取到 {len(options)} 个未过期的期权合约----用于采集数据")
    
    # 按时间过滤期权合约
    filtered_options = filter_by_time(options, test)
    logger.info(f"时间过滤后剩余 {len(filtered_options)} 个期权合约----用于采集数据")
    
    return filtered_options

if __name__ == '__main__':
    # 以下为测试代码
    # print(len(get_active_options()))

#     from db import init_all_dbs
#     init_all_dbs() # 确保数据库和表存在
    fetch_and_save_contracts()
    # fetch_and_save_options()
    # active_list = get_active_contracts()
    # print("\n筛选出的活跃合约列表:")
    # print(len(active_list[0]),len(active_list[1]),len(active_list[2]),len(active_list[3])) 
    # print(active_list[3])
    
    # 测试期权合约获取
    # fetch_and_save_options()
    # active_options = get_active_options()
    # print("\n筛选出的活跃期权合约列表:")
    # print(f"共 {len(active_options)} 个期权合约")
    # print(active_options)