#!/bin/bash

# 数据采集服务重启脚本

SERVICE_NAME="qhdatacj"
LOG_FILE="/var/log/qhdatacj_restart.log"

echo "$(date): 开始重启 $SERVICE_NAME 服务" | tee -a $LOG_FILE

# 检查服务状态
echo "检查服务状态..."
systemctl status $SERVICE_NAME

# 停止服务
echo "停止服务..."
systemctl stop $SERVICE_NAME
sleep 5

# 检查进程是否还在运行
PID=$(pgrep -f "main.py")
if [ ! -z "$PID" ]; then
    echo "强制终止进程 $PID..."
    kill -9 $PID
    sleep 2
fi

# 清理内存
echo "清理系统缓存..."
sync
echo 3 > /proc/sys/vm/drop_caches

# 启动服务
echo "启动服务..."
systemctl start $SERVICE_NAME
sleep 3

# 检查服务状态
echo "检查服务状态..."
systemctl status $SERVICE_NAME

# 检查内存使用
echo "检查内存使用情况..."
free -h

echo "$(date): 服务重启完成" | tee -a $LOG_FILE
echo "----------------------------------------" | tee -a $LOG_FILE 