#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
内存监控脚本
用于实时监控数据采集程序的内存使用情况
"""

import psutil
import time
import os
import sys
from datetime import datetime
import argparse

def get_process_memory(pid):
    """获取指定进程的内存使用情况"""
    try:
        process = psutil.Process(pid)
        memory_info = process.memory_info()
        memory_mb = memory_info.rss / 1024 / 1024
        memory_percent = process.memory_percent()
        return memory_mb, memory_percent
    except psutil.NoSuchProcess:
        return None, None
    except Exception as e:
        print(f"获取进程内存信息失败: {e}")
        return None, None

def get_system_memory():
    """获取系统内存使用情况"""
    try:
        memory = psutil.virtual_memory()
        return {
            'total': memory.total / 1024 / 1024,  # MB
            'available': memory.available / 1024 / 1024,  # MB
            'used': memory.used / 1024 / 1024,  # MB
            'percent': memory.percent
        }
    except Exception as e:
        print(f"获取系统内存信息失败: {e}")
        return None

def monitor_process(pid, interval=10, duration=None):
    """监控指定进程的内存使用"""
    print(f"开始监控进程 {pid}")
    print(f"监控间隔: {interval} 秒")
    if duration:
        print(f"监控时长: {duration} 秒")
    print("-" * 80)
    
    start_time = time.time()
    max_memory = 0
    min_memory = float('inf')
    
    try:
        while True:
            # 检查是否超过监控时长
            if duration and (time.time() - start_time) > duration:
                break
            
            # 获取进程内存信息
            memory_mb, memory_percent = get_process_memory(pid)
            
            if memory_mb is None:
                print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] 进程 {pid} 不存在")
                break
            
            # 获取系统内存信息
            system_memory = get_system_memory()
            
            # 更新最大最小内存
            max_memory = max(max_memory, memory_mb)
            min_memory = min(min_memory, memory_mb)
            
            # 显示内存信息
            print(f"[{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}] "
                  f"进程内存: {memory_mb:.2f} MB ({memory_percent:.1f}%) | "
                  f"系统内存: {system_memory['used']:.0f}/{system_memory['total']:.0f} MB ({system_memory['percent']:.1f}%) | "
                  f"可用: {system_memory['available']:.0f} MB")
            
            # 内存使用过高警告
            if memory_mb > 1024:  # 超过1GB
                print(f"⚠️  警告: 进程内存使用过高 ({memory_mb:.2f} MB)")
            
            if system_memory['percent'] > 80:  # 系统内存使用超过80%
                print(f"⚠️  警告: 系统内存使用过高 ({system_memory['percent']:.1f}%)")
            
            time.sleep(interval)
            
    except KeyboardInterrupt:
        print("\n监控已停止")
    
    # 显示统计信息
    print("-" * 80)
    print(f"监控统计:")
    print(f"  监控时长: {time.time() - start_time:.1f} 秒")
    print(f"  最大内存使用: {max_memory:.2f} MB")
    print(f"  最小内存使用: {min_memory:.2f} MB")
    print(f"  平均内存使用: {(max_memory + min_memory) / 2:.2f} MB")

def find_process_by_name(name):
    """根据进程名查找进程ID"""
    try:
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            if name in proc.info['name'] or (proc.info['cmdline'] and any(name in cmd for cmd in proc.info['cmdline'])):
                return proc.info['pid']
    except Exception as e:
        print(f"查找进程失败: {e}")
    return None

def main():
    parser = argparse.ArgumentParser(description='内存监控工具')
    parser.add_argument('--pid', type=int, help='要监控的进程ID')
    parser.add_argument('--name', type=str, help='要监控的进程名（包含此名称的进程）')
    parser.add_argument('--interval', type=int, default=10, help='监控间隔（秒，默认10秒）')
    parser.add_argument('--duration', type=int, help='监控时长（秒，默认无限）')
    parser.add_argument('--list', action='store_true', help='列出所有Python进程')
    
    args = parser.parse_args()
    
    if args.list:
        print("Python进程列表:")
        print("-" * 60)
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            if 'python' in proc.info['name'].lower():
                cmdline = ' '.join(proc.info['cmdline']) if proc.info['cmdline'] else ''
                print(f"PID: {proc.info['pid']:>6} | 名称: {proc.info['name']:<15} | 命令行: {cmdline[:50]}")
        return
    
    # 确定要监控的进程ID
    pid = args.pid
    if not pid and args.name:
        pid = find_process_by_name(args.name)
        if pid:
            print(f"找到进程: {args.name} (PID: {pid})")
        else:
            print(f"未找到包含 '{args.name}' 的进程")
            return
    
    if not pid:
        print("请指定进程ID (--pid) 或进程名 (--name)")
        print("使用 --list 查看所有Python进程")
        return
    
    # 开始监控
    monitor_process(pid, args.interval, args.duration)

if __name__ == "__main__":
    main() 